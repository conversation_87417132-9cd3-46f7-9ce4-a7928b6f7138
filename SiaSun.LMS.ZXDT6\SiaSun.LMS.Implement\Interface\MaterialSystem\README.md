# 物资系统接口集成说明

## 概述

本模块实现了在任务完成时自动调用物资系统接口的功能，支持入库、出库、移库三种任务类型的接口回调。

## 功能特性

- **策略模式设计**：每种任务类型对应独立的接口调用策略
- **工厂模式创建**：统一的策略创建和管理
- **配置化管理**：支持通过系统参数控制接口调用行为
- **重试机制**：支持接口调用失败时的自动重试
- **异常隔离**：接口调用失败不影响任务正常完成
- **详细日志**：完整的调用日志记录，便于问题排查

## 架构设计

### 核心组件

1. **IMaterialSystemInterfaceStrategy** - 接口策略基类
2. **MaterialSystemInterfaceFactory** - 策略工厂类
3. **InboundInterfaceStrategy** - 入库接口策略
4. **OutboundInterfaceStrategy** - 出库接口策略
5. **TransferInterfaceStrategy** - 移库接口策略
6. **MaterialSystemInterfaceConfig** - 配置管理类

### 调用流程

```
任务完成 -> ManageComplete -> CallMaterialSystemInterface -> 
策略工厂 -> 具体策略 -> 物资系统接口 -> 返回结果
```

## 配置参数

### 系统参数配置

在 `SYS_PARAMETER` 表中添加以下配置参数：

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| EnableMaterialSystemInterface | false | 是否启用物资系统接口调用 |
| EnableInboundInterface | true | 是否启用入库接口 |
| EnableOutboundInterface | true | 是否启用出库接口 |
| EnableTransferInterface | true | 是否启用移库接口 |
| InterfaceTimeoutSeconds | 30 | 接口调用超时时间（秒） |
| InterfaceRetryCount | 3 | 接口调用失败重试次数 |
| EnableAsyncInterfaceCall | false | 是否启用异步接口调用 |

### 配置示例

```sql
-- 启用物资系统接口调用
INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_REMARK) 
VALUES ('EnableMaterialSystemInterface', 'true', '启用物资系统接口调用');

-- 设置接口超时时间为60秒
INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_REMARK) 
VALUES ('InterfaceTimeoutSeconds', '60', '接口调用超时时间');

-- 设置重试次数为5次
INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_REMARK) 
VALUES ('InterfaceRetryCount', '5', '接口调用失败重试次数');
```

## 任务类型映射

| 任务类型 | MANAGE_TYPE_INOUT | 调用接口 | 说明 |
|----------|-------------------|----------|------|
| 入库任务 | 1 | InboundReceiptCompleteCallback | 入库单完成上报 |
| 出库任务 | 2 | OutboundReceiptCompleteCallback | 出库单完成上报 |
| 移库任务 | 3 | TransferResultCallback | 移库结果上报 |

## 使用方法

### 1. 启用接口调用

```sql
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = 'true' 
WHERE PARAMETER_KEY = 'EnableMaterialSystemInterface';
```

### 2. 测试接口调用

```csharp
// 运行所有测试
MaterialSystemInterfaceTest.RunAllTests();

// 测试特定接口
MaterialSystemInterfaceTest.TestInboundInterface();
MaterialSystemInterfaceTest.TestOutboundInterface();
MaterialSystemInterfaceTest.TestTransferInterface();
```

### 3. 查看配置信息

```csharp
// 打印当前配置到日志
MaterialSystemInterfaceConfig.LogCurrentConfigs();

// 验证配置有效性
var (isValid, errors) = MaterialSystemInterfaceConfig.ValidateConfigs();
if (!isValid)
{
    foreach (var error in errors)
    {
        Console.WriteLine($"配置错误：{error}");
    }
}
```

## 日志监控

### 关键日志信息

- **成功调用**：`物资系统接口调用成功_任务ID[xxx]_任务类型[xxx]`
- **调用失败**：`物资系统接口调用失败_任务ID[xxx]_错误信息[xxx]`
- **重试成功**：`物资系统接口调用重试成功_任务ID[xxx]_重试次数[x]`
- **配置禁用**：`物资系统接口调用已禁用_任务ID[xxx]`
- **类型不支持**：`不支持的任务类型_任务ID[xxx]_类型[xxx]`

### 日志级别

- **Info**：正常调用成功、重试成功
- **Warn**：调用失败准备重试、不支持的任务类型
- **Error**：调用异常、达到最大重试次数
- **Debug**：接口禁用、策略创建

## 故障排查

### 常见问题

1. **接口调用失败**
   - 检查网络连接
   - 验证接口地址配置
   - 查看详细错误日志

2. **接口未调用**
   - 检查 `EnableMaterialSystemInterface` 配置
   - 检查具体任务类型的接口开关
   - 确认任务类型映射正确

3. **调用超时**
   - 调整 `InterfaceTimeoutSeconds` 参数
   - 检查网络延迟
   - 优化接口性能

### 调试方法

```csharp
// 1. 检查配置
MaterialSystemInterfaceConfig.LogCurrentConfigs();

// 2. 验证策略创建
var strategy = MaterialSystemInterfaceFactory.CreateStrategy("1");
Console.WriteLine($"策略创建结果：{strategy?.StrategyName ?? "失败"}");

// 3. 手动测试接口
MaterialSystemInterfaceTest.TestInboundInterface();
```

## 扩展开发

### 添加新的任务类型

1. 实现 `IMaterialSystemInterfaceStrategy` 接口
2. 在 `MaterialSystemInterfaceFactory` 中添加创建逻辑
3. 更新配置管理类添加相应配置项
4. 添加相应的测试用例

### 示例：添加盘点任务接口

```csharp
public class InventoryInterfaceStrategy : IMaterialSystemInterfaceStrategy
{
    public string StrategyName => "盘点任务接口策略";
    
    public bool CallInterface(MANAGE_MAIN manageMain, IList<MANAGE_LIST> manageList, out string message)
    {
        // 实现盘点接口调用逻辑
        // ...
    }
}
```

## 注意事项

1. **事务处理**：接口调用失败不会回滚任务完成事务
2. **性能影响**：接口调用会增加任务完成时间，建议启用异步调用
3. **数据一致性**：确保传递给接口的数据准确完整
4. **错误处理**：接口调用异常不应影响业务流程
5. **监控告警**：建议对接口调用失败率进行监控告警

## 版本历史

- **v1.0.0** - 初始版本，支持入库、出库、移库三种接口
- 支持配置化管理和重试机制
- 完整的日志记录和错误处理
