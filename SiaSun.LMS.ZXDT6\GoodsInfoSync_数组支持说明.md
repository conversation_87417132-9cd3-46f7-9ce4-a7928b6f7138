# GoodsInfoSync 数组支持功能说明

## 概述

已成功修改 `SiaSun.LMS.Implement.Interface.WMS.GoodsInfoSync.cs` 文件，使其能够接收和处理数组格式的输入参数，同时保持对原有单个对象格式的向后兼容性。

## 主要修改内容

### 1. 新增数据结构

#### BatchInputParam 类
```csharp
/// <summary>
/// 批量输入参数包装类
/// </summary>
class BatchInputParam
{
    /// <summary>
    /// 物料信息数组
    /// </summary>
    public List<InputParam> goodsList { get; set; }
}
```

#### GoodsProcessResult 类
```csharp
/// <summary>
/// 单个物料处理结果类
/// </summary>
class GoodsProcessResult
{
    /// <summary>
    /// 物料编码
    /// </summary>
    public string goodsCode { get; set; }
    /// <summary>
    /// 处理状态：0-成功，1-失败
    /// </summary>
    public int status { get; set; }
    /// <summary>
    /// 处理消息
    /// </summary>
    public string message { get; set; }
}
```

### 2. 修改输出参数结构

在原有的 `OutputParam` 类中新增了 `results` 字段：
```csharp
class OutputParam
{
    public int code { get; set; }
    public string msg { get; set; }
    public string traceId { get; set; }
    /// <summary>
    /// 批量处理结果详情
    /// </summary>
    public List<GoodsProcessResult> results { get; set; }
}
```

### 3. 重构主要接口方法

#### 智能输入解析
- 首先尝试解析为批量格式 (`BatchInputParam`)
- 如果失败，则尝试解析为单个对象格式 (`InputParam`) - 保持向后兼容
- 如果两种格式都解析失败，返回错误信息

#### 批量处理逻辑
- 循环处理每个物料信息
- 为每个物料生成独立的处理结果
- 统计成功和失败的数量
- 根据处理结果设置整体返回状态

### 4. 新增 ProcessSingleGoods 方法

将原有的单个物料处理逻辑提取为独立方法：
- 参数验证（物料编码、名称、状态等）
- 数据库操作（创建或更新物料信息）
- 处理嵌套的单位信息和品牌信息
- 异常处理和日志记录

## 输入格式支持

### 单个物料格式（向后兼容）
```json
{
    "goodsCode": "TEST001",
    "name": "测试物料001",
    "goodsStatus": 1,
    "unitName": "个",
    "goodsVersion": "V1.0",
    "brandVOs": [
        {
            "brandName": "测试品牌",
            "purchasesNum": "1",
            "inboundQuantity": 100
        }
    ]
}
```

### 批量物料格式（新增）
```json
{
    "goodsList": [
        {
            "goodsCode": "BATCH001",
            "name": "批量测试物料001",
            "goodsStatus": 1,
            "unitName": "个",
            "goodsVersion": "V1.0",
            "brandVOs": [
                {
                    "brandName": "品牌A",
                    "purchasesNum": "1",
                    "inboundQuantity": 50
                }
            ]
        },
        {
            "goodsCode": "BATCH002",
            "name": "批量测试物料002",
            "goodsStatus": 0,
            "unitName": "套",
            "goodsVersion": "V2.0",
            "brandVOs": [
                {
                    "brandName": "品牌B",
                    "purchasesNum": "2",
                    "inboundQuantity": 75
                }
            ]
        }
    ]
}
```

## 输出格式

### 成功响应示例
```json
{
    "code": 0,
    "msg": "批量处理完成_总数[2]_成功[2]_失败[0]",
    "traceId": "12345678-1234-1234-1234-123456789012",
    "results": [
        {
            "goodsCode": "BATCH001",
            "status": 0,
            "message": "物资信息创建成功"
        },
        {
            "goodsCode": "BATCH002",
            "status": 0,
            "message": "物资信息更新成功"
        }
    ]
}
```

### 部分失败响应示例
```json
{
    "code": 1,
    "msg": "批量处理部分成功_总数[3]_成功[2]_失败[1]",
    "traceId": "12345678-1234-1234-1234-123456789012",
    "results": [
        {
            "goodsCode": "VALID001",
            "status": 0,
            "message": "物资信息创建成功"
        },
        {
            "goodsCode": "未知",
            "status": 1,
            "message": "物料编码[goodsCode]不能为空"
        },
        {
            "goodsCode": "VALID002",
            "status": 0,
            "message": "物资信息更新成功"
        }
    ]
}
```

## 返回状态码说明

- `code = 0`: 全部成功
- `code = 1`: 部分成功（有成功也有失败）
- `code = 2`: 全部失败或输入解析错误

## 业务逻辑保持

- 保持原有的数据库操作逻辑
- 保持原有的参数验证规则
- 保持原有的单位信息和品牌信息处理逻辑
- 保持原有的错误处理和日志记录机制

## 向后兼容性

- 完全兼容原有的单个物料信息输入格式
- 原有的调用方式无需修改即可继续使用
- 输出格式在原有基础上扩展，不影响现有解析逻辑

## 测试建议

1. 使用 `TestGoodsInfoSyncArray.cs` 中提供的测试数据进行功能验证
2. 测试单个物料信息处理（向后兼容性）
3. 测试批量物料信息处理
4. 测试包含错误数据的批量处理
5. 验证数据库操作的正确性
6. 检查日志记录的完整性

## 性能考虑

- 批量处理时，每个物料信息独立处理，确保单个失败不影响其他物料
- 保持事务的原子性，每个物料的数据库操作独立提交
- 详细的日志记录有助于问题排查和性能监控
