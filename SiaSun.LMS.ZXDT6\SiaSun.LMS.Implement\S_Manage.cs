﻿using System;
using System.Collections.Generic;
using System.ServiceModel;
using System.Data;
using System.Collections;
using System.Linq;
using System.Reflection;
using SiaSun.LMS.Model;
using SiaSun.LMS.Interface;
using System.Diagnostics;
using SiaSun.LMS.Implement.Interface.MaterialSystem;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true,
                     InstanceContextMode = InstanceContextMode.Single,
                     ConcurrencyMode = ConcurrencyMode.Single,
                     MaxItemsInObjectGraph = int.MaxValue)]
    public class S_Manage : I_Manage
    {
        public static readonly object transFinishLockObj=new object();

        /// <summary>
        /// 执行计划事件
        /// </summary>
        /// <param name="mPLAN_ACTION_EXCUTE">计划事件模型</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanEventExecute(SiaSun.LMS.Model.PLAN_ACTION_EXCUTE mPLAN_ACTION_EXCUTE, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            if (null == mPLAN_ACTION_EXCUTE)
            {
                bResult = false;

                sResult = string.Format("操作方法不存在");

                return false;
            }

            bResult = EventExecute(mPLAN_ACTION_EXCUTE.ACTION_EVENT, out sResult);

            return bResult;
        }

        /// <summary>
        /// 执行任务事件
        /// </summary>
        /// <param name="mt">任务事件模型</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool ManageEventExecute(SiaSun.LMS.Model.MANAGE_ACTION_EXCUTE mt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            bResult = EventExecute(mt.ACTION_EVENT, out sResult);

            if (null == mt)
            {
                bResult = false;

                sResult = string.Format("mt不能为空");

                return false;
            }

            return bResult;
        }

        /// <summary>
        /// 执行事件
        /// </summary>
        /// <param name="sEvent">事件源</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        private bool EventExecute(string sEvent, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;


            SiaSun.LMS.Common.Compiler cp = new SiaSun.LMS.Common.Compiler();

            string sPath = AppDomain.CurrentDomain.BaseDirectory;

            cp.lsRef.Add(sPath + @"\WinService.exe");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Interface.dll");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Implement.dll");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Model.dll");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Common.dll");

            cp.lsUsing.Add("SiaSun.LMS.WinService");

            cp.strCode = sEvent;

            bResult = cp.Excute(out sResult);


            return bResult;
        }

        /// <summary>
        /// 下达货架指定排起始列到终止列内物料的下架任务
        /// 修货架期间专用批量下架方法
        /// </summary>
        public string ContinusManageDown(Model.SYS_USER user, int row, int startColumn, int endColumn)
        {
            string message = "success";
            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                DataTable dtManageConfirm = S_Base.sBase.sDatabase.GetList(string.Format("select 0 from MANAGE_MAIN where START_CELL_ID in (select CELL_ID from WH_CELL where CELL_Z={0}) or END_CELL_ID in (select CELL_ID from WH_CELL where CELL_Z={0}) ", row));
                if (dtManageConfirm != null && dtManageConfirm.Rows.Count != 0)
                {
                    message = string.Format("所选货架排[{0}]存在未完成的出入库任务，请确保任务完成后再批量下架", row);
                    S_Base.sBase.sDatabase.RollBackTransaction();
                    return message;
                }

                DataTable dtStorageEnable = S_Base.sBase.sDatabase.GetList(string.Format("select STORAGE_ID,STOCK_BARCODE,CELL_CODE,WH_CELL.CELL_ID,CELL_X,CELL_Y,CELL_Z from STORAGE_MAIN left join WH_CELL on STORAGE_MAIN.CELL_ID = WH_CELL.CELL_ID where CELL_Z={0} and CELL_X between {1} and {2}", row, startColumn, endColumn));
                if (dtStorageEnable == null || dtStorageEnable.Rows.Count == 0)
                {
                    message = string.Format("所选货架排[{0}]_起始列[{1}]_到终止排[{2}]内没有库存", row, startColumn, endColumn);
                    S_Base.sBase.sDatabase.RollBackTransaction();
                    return message;
                }

                foreach (DataRow dr in dtStorageEnable.Rows)
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModel(int.Parse(dr["STORAGE_ID"].ToString()));
                    if (mSTORAGE_MAIN == null)
                    {
                        message = string.Format("货位[{0}]的库存信息未找到", dr["CELL_CODE"].ToString());
                        S_Base.sBase.sDatabase.RollBackTransaction();
                        return message;
                    }
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        message = string.Format("货位[{0}]_箱条码[{1}]的库存列表信息未找到", dr["CELL_CODE"].ToString(), mSTORAGE_MAIN.STOCK_BARCODE);
                        S_Base.sBase.sDatabase.RollBackTransaction();
                        return message;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = new MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = 20028;//21077
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = "5";
                    mMANAGE_MAIN.MANAGE_OPERATOR = user.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_REMARK = "维修货架下架";
                    mMANAGE_MAIN.MANAGE_SOURCE = "WES";
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageDown.ToString();
                    mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<MANAGE_LIST>();
                    foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new MANAGE_LIST();

                        mMANAGE_LIST.BOX_BARCODE = itemSTORAGE_LIST.BOX_BARCODE;
                        mMANAGE_LIST.GOODS_ID = itemSTORAGE_LIST.GOODS_ID;
                        mMANAGE_LIST.GOODS_PROPERTY1 = itemSTORAGE_LIST.GOODS_PROPERTY1;
                        mMANAGE_LIST.GOODS_PROPERTY2 = itemSTORAGE_LIST.GOODS_PROPERTY2;
                        mMANAGE_LIST.GOODS_PROPERTY3 = itemSTORAGE_LIST.GOODS_PROPERTY3;
                        mMANAGE_LIST.GOODS_PROPERTY4 = itemSTORAGE_LIST.GOODS_PROPERTY4;
                        mMANAGE_LIST.GOODS_PROPERTY5 = itemSTORAGE_LIST.GOODS_PROPERTY5;
                        mMANAGE_LIST.GOODS_PROPERTY6 = itemSTORAGE_LIST.GOODS_PROPERTY6;
                        mMANAGE_LIST.GOODS_PROPERTY7 = itemSTORAGE_LIST.GOODS_PROPERTY7;
                        mMANAGE_LIST.GOODS_PROPERTY8 = itemSTORAGE_LIST.GOODS_PROPERTY8;
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        mMANAGE_LIST.PLAN_LIST_ID = 0;
                        mMANAGE_LIST.STORAGE_LIST_ID = itemSTORAGE_LIST.STORAGE_LIST_ID;

                        lsMANAGE_LIST.Add(mMANAGE_LIST);
                    }

                    //bool bResult = new SiaSun.LMS.Implement.ManageDown().ManageCreate(mMANAGE_MAIN, lsMANAGE_LIST, false, false, true, true, out message);
                    //if (!bResult)
                    //{
                    //    S_Base.sBase.sDatabase.RollBackTransaction();
                    //    return message;
                    //}
                }

                message = "success|" + dtStorageEnable.Rows.Count.ToString();
                S_Base.sBase.sDatabase.CommitTransaction();
            }
            catch (Exception ex)
            {
                message = string.Format("S_ManageService.ContinusManageDown():发生异常_{0}", ex.Message);
                S_Base.sBase.sDatabase.RollBackTransaction();
            }
            finally
            {
                //log
                //base.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, message.Contains("success"), message);

                if (!message.Contains("success"))
                {
                    S_Base.sBase.Log.Error(message);
                }
            }

            return message;
        }

        /// <summary>
        /// 生成任务
        /// </summary>
        public bool ManageCreate(Model.MANAGE_MAIN mMANAGE_MAIN,
                 List<Model.MANAGE_LIST> lsMANAGE_LIST,
                 bool raiseTrans,
                 bool checkStorage,
                 bool checkManage,
                 bool checkCellStatus,
                 bool autoComplete,
                 bool autoControl,
                 bool doubleInAutoMove,
                 out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(raiseTrans);

                if (mMANAGE_MAIN == null || string.IsNullOrEmpty(mMANAGE_MAIN.MANAGE_TYPE_CODE) || string.IsNullOrEmpty(mMANAGE_MAIN.STOCK_BARCODE))
                {
                    result = false;
                    message = string.Format("传入任务信息为空或不完整");
                    return result;
                }

                string validRegexString = string.Empty;
                if (S_Base.sBase.sSystem.GetSysParameter("StockBarcodeValidRegex", out validRegexString) &&
                    !Common.RegexValid.IsValidate(mMANAGE_MAIN.STOCK_BARCODE, validRegexString))
                {
                    result = false;
                    message = string.Format("托盘条码未通过格式校验_托盘条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                    return result;
                }

                if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count < 1)
                {
                    //库存移动类的任务可以根据库存来自动生成任务列表
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageUp.ToString() ||
                        mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageDown.ToString() ||
                        mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageMove.ToString())
                    {
                        Model.STORAGE_MAIN mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                        if (mSTORAGE_MAIN == null)
                        {
                            result = false;
                            message = string.Format("生成[{0}]时未找到库存信息_条码[{1}]", mMANAGE_MAIN.MANAGE_TYPE_CODE, mMANAGE_MAIN.STOCK_BARCODE);
                            return result;
                        }

                        IList<Model.STORAGE_LIST> lsSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                        if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count < 1)
                        {
                            result = false;
                            message = string.Format("生成[{0}]时未找到库存列表信息_条码[{1}]", mMANAGE_MAIN.MANAGE_TYPE_CODE, mMANAGE_MAIN.STOCK_BARCODE);
                            return result;
                        }

                        lsMANAGE_LIST = new List<MANAGE_LIST>();
                        foreach (var mSTORAGE_LIST in lsSTORAGE_LIST)
                        {
                            Model.MANAGE_LIST mMANAGE_LIST = new MANAGE_LIST();
                            mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                            mMANAGE_LIST.STORAGE_LIST_ID = mSTORAGE_LIST.STORAGE_LIST_ID;
                            mMANAGE_LIST.PLAN_LIST_ID = 0;
                            mMANAGE_LIST.GOODS_ID = mSTORAGE_LIST.GOODS_ID;
                            mMANAGE_LIST.BOX_BARCODE = mSTORAGE_LIST.BOX_BARCODE;
                            mMANAGE_LIST.MANAGE_LIST_QUANTITY = mSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                            mMANAGE_LIST.MANAGE_LIST_REMARK = mSTORAGE_LIST.STORAGE_LIST_REMARK;
                            result = S_Base.sBase.sSystem.GoodsPropertySetValue<Model.MANAGE_LIST>(mSTORAGE_LIST.GOODS_ID, mMANAGE_LIST, mSTORAGE_LIST, out message);
                            if (!result)
                            {
                                return result;
                            }

                            lsMANAGE_LIST.Add(mMANAGE_LIST);
                        }
                    }
                    else
                    {
                        result = false;
                        message = string.Format("传入任务列表为空_条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                        return result;
                    }
                }

                if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageAdjust.ToString())
                {
                    var manageTypeGroup = lsMANAGE_LIST.Select(r => r.MANAGE_LIST_QUANTITY >= 0 ? Enum.MANAGE_TYPE.ManageAdjustOut.ToString() : Enum.MANAGE_TYPE.ManageAdjustIn.ToString()).Distinct();
                    if (manageTypeGroup.Count() != 1)
                    {
                        result = false;
                        message = string.Format("托盘中包含盘盈盘亏非单一情况_请分别操作_托盘条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                        return result;
                    }

                    mMANAGE_MAIN.MANAGE_TYPE_CODE = manageTypeGroup.ToList()[0];

                    foreach (var item in lsMANAGE_LIST)
                    {
                        if (!decimal.TryParse(S_Base.sBase.sDatabase.IsExistData($"select STORAGE_LIST_QUANTITY_UNLOCK from V_STORAGE_LIST where STORAGE_LIST_ID = {item.STORAGE_LIST_ID}"), out decimal unlcokQty) ||
                            unlcokQty < item.MANAGE_LIST_QUANTITY)
                        {
                            result = false;
                            message = string.Format("可用数量获取失败或者调整出库数量大于可用数量_托盘条码[{0}]_物料条码[{1}]", mMANAGE_MAIN.STOCK_BARCODE, item.BOX_BARCODE);
                            return result;
                        }

                        item.MANAGE_LIST_QUANTITY = Math.Abs(item.MANAGE_LIST_QUANTITY);
                    }
                }

                Model.MANAGE_TYPE mMANAGE_TYPE = S_Base.sBase.pMANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                if (mMANAGE_TYPE == null)
                {
                    result = false;
                    message = string.Format("未找到任务类型_类型编码[{0}]", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    return result;
                }

                if (checkStorage)
                {
                    bool storageExist = !string.IsNullOrEmpty(S_Base.sBase.sDatabase.IsExistData($"select * from STORAGE_MAIN where STOCK_BARCODE='{mMANAGE_MAIN.STOCK_BARCODE}'"));

                    if (mMANAGE_TYPE.MANAGE_TYPE_INOUT == Enum.MANAGE_TYPE_INOUT.In.ToString("d") && storageExist)
                    {
                        result = false;
                        message = string.Format("托盘[{0}]存在库存 ", mMANAGE_MAIN.STOCK_BARCODE);
                        return result;
                    }

                    //对于出库、上架、下架和移库 要校验任务数量和库存数量是否符合逻辑
                    if (mMANAGE_TYPE.MANAGE_TYPE_INOUT == Enum.MANAGE_TYPE_INOUT.Out.ToString("d") ||
                        mMANAGE_TYPE.MANAGE_TYPE_INOUT == Enum.MANAGE_TYPE_INOUT.Move.ToString("d"))
                    {
                        if (!storageExist)
                        {
                            result = false;
                            message = string.Format("托盘[{0}]不存在库存 ", mMANAGE_MAIN.STOCK_BARCODE);
                            return result;
                        }

                        foreach (var item in lsMANAGE_LIST)
                        {
                            Model.STORAGE_LIST mSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetModel(item.STORAGE_LIST_ID);
                            if (mSTORAGE_LIST == null)
                            {
                                result = false;
                                message = string.Format("源库存单未找到_任务单ID[{0}]_托盘条码[{1}]", item.MANAGE_LIST_ID, mMANAGE_MAIN.STOCK_BARCODE);
                                return result;
                            }

                            if (item.MANAGE_LIST_QUANTITY > mSTORAGE_LIST.STORAGE_LIST_QUANTITY)
                            {
                                result = false;
                                message = string.Format("任务单数量[{0}]不能大于源库存单数量[{1}]_任务单ID[{2}]_托盘条码[{3}]",
                                    item.MANAGE_LIST_QUANTITY > mSTORAGE_LIST.STORAGE_LIST_QUANTITY, item.MANAGE_LIST_ID, mMANAGE_MAIN.STOCK_BARCODE);
                                return result;
                            }
                        }
                    }
                }

                Model.WH_CELL mWH_CELL_START = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                if (mWH_CELL_START == null)
                {
                    result = false;
                    message = string.Format("未找到起点站台_站台ID[{0}]", mMANAGE_MAIN.START_CELL_ID);
                    return result;
                }
                Model.WH_AREA mWH_AREA_START = S_Base.sBase.pWH_AREA.GetModel(mWH_CELL_START.AREA_ID);
                if (mWH_AREA_START == null)
                {
                    result = false;
                    message = string.Format("未找到起点站台所属区域_站台[{0}]", mWH_CELL_START.CELL_CODE);
                    return result;
                }

                //如果起点是双伸货位内层，检查外层是否有入库任务
                if(mWH_CELL_START.SHELF_TYPE == Enum.SHELF_TYPE.DoubleIn.ToString())
                {
                    var checkNeighbourInTask = S_Base.sBase.sDatabase.GetList($"select * from MANAGE_MAIN where END_CELL_ID={mWH_CELL_START.SHELF_NEIGHBOUR}");
                    if (checkNeighbourInTask != null && checkNeighbourInTask.Rows.Count > 1)
                    {
                        result = false;
                        message = string.Format("托盘条码[{0}]双伸货位内层[{1}]无法出库_双伸外层存在入库任务[{2}]",
                            mMANAGE_MAIN.STOCK_BARCODE,
                            mWH_CELL_START.CELL_CODE,
                            checkNeighbourInTask.Rows[0]["STOCK_BARCODE"].ToString());
                        return result;
                    }
                }

                if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageAdjust.ToString()
                    && !mWH_AREA_START.AREA_CODE.StartsWith(Enum.AreaCode.ZCQ.ToString()))
                {
                    result = false;
                    message = string.Format("库存所在位置不允许调整_托盘条码[{0}]_所在位置[{1}-{2}]", mMANAGE_MAIN.STOCK_BARCODE, mWH_AREA_START.AREA_CODE, mWH_CELL_START.CELL_CODE);
                    return result;
                }

                Model.WH_CELL mWH_CELL_END = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);
                if (autoControl && mWH_CELL_END == null)
                {
                    result = false;
                    message = string.Format("未找到终点站台_站台ID[{0}]", mMANAGE_MAIN.END_CELL_ID);
                    return result;
                }

                if (checkCellStatus)
                {
                    if (mWH_CELL_START != null
                        && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                        && (mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Have.ToString() || mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Pallet.ToString())
                        && mWH_CELL_START.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                    {
                        result = false;
                        message = string.Format("起始位置[{0}]不可用", mWH_CELL_START.CELL_CODE);
                        return result;
                    }
                    if (mWH_CELL_END != null
                        && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                        && mWH_CELL_END.CELL_STATUS != Enum.CELL_STATUS.Nohave.ToString()
                        && mWH_CELL_END.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                    {
                        result = false;
                        message = string.Format("终止位置[{0}]不可用", mWH_CELL_END.CELL_CODE);
                        return result;
                    }
                }
  
                if (checkManage && S_Base.sBase.pMANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE) != null)
                {
                    result = false;
                    message = string.Format("托盘[{0}]存在任务", mMANAGE_MAIN.STOCK_BARCODE);
                    return result;
                }

                S_Base.sBase.pMANAGE_MAIN.Add(mMANAGE_MAIN);

                Model.PLAN_MAIN mPLAN_MAIN = S_Base.sBase.pPLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);

                foreach (Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    if (mPLAN_MAIN != null)
                    {
                        Model.PLAN_LIST mIO_PLAN_LIST = S_Base.sBase.pPLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (mIO_PLAN_LIST != null)
                        {
                            mIO_PLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                            S_Base.sBase.pPLAN_LIST.Update(mIO_PLAN_LIST);
                        }
                    }
                    S_Base.sBase.pMANAGE_LIST.Add(mMANAGE_LIST);
                }

                if (autoComplete)
                {
                    result = ManageComplete(mMANAGE_MAIN.MANAGE_ID, false, out message);
                    if (!result)
                    {
                        return result;
                    }
                }
                else
                {
                    if (mWH_CELL_START != null )
                    {
                        result = S_Base.sBase.sManage.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, Enum.RUN_STATUS.Selected.ToString(), out message);
                        if (!result)
                        {
                            message = string.Format("更新开始货位[{0}]状态错误_信息[{1}]", mWH_CELL_START.CELL_CODE, message);
                            return result;
                        }
                    }

                    if (mWH_CELL_END != null)
                    {
                        result = S_Base.sBase.sManage.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, Enum.RUN_STATUS.Selected.ToString(), out message);
                        if (!result)
                        {
                            message = string.Format("更新结束货位[{0}]状态错误_信息[{1}]", mWH_CELL_END.CELL_CODE, message);
                            return result;
                        }
                    }
                }

                //双伸货位内层自动移库
                if(doubleInAutoMove && mWH_CELL_START.SHELF_TYPE == Enum.SHELF_TYPE.DoubleIn.ToString() 
                    && int.TryParse(mWH_CELL_START.SHELF_NEIGHBOUR,out int neighbourCellId))
                {
                    var neiStorageMain = S_Base.sBase.pSTORAGE_MAIN.GetModelCellID(neighbourCellId);
                    if(neiStorageMain != null)
                    {
                        //首先判断是否有到达外层的入库任务
                        var neiManageMainIn = S_Base.sBase.sDatabase.GetList($"select * from MANAGE_MAIN where END_CELL_ID = {neighbourCellId}");
                        if (neiManageMainIn != null && neiManageMainIn.Rows.Count > 0)
                        {
                            result = false;
                            message = $"下达[{mMANAGE_MAIN.STOCK_BARCODE}]任务时外层货位存在入库任务[{neiManageMainIn.Rows[0]["STOCK_BARCODE"].ToString()}]";
                            return result;
                        }

                        //判断是否已经下达了倒库任务
                        var neiManageMain = S_Base.sBase.pMANAGE_MAIN.GetModelStockBarcode(neiStorageMain.STOCK_BARCODE);
                        if (neiManageMain != null)
                        {
                            if(neiManageMain.END_CELL_ID == mWH_CELL_START.CELL_ID)
                            {
                                result = false;
                                message = $"下达[{mMANAGE_MAIN.STOCK_BARCODE}]任务时外层货位存在入库任务[{neiManageMain.STOCK_BARCODE}]";
                                return result;
                            }
                        }
                        else
                        {
                            neiManageMain = new Model.MANAGE_MAIN()
                            {
                                FULL_FLAG = "",
                                STOCK_BARCODE = neiStorageMain.STOCK_BARCODE,
                                GOODS_TEMPLATE_ID = 0,
                                MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                                MANAGE_CONFIRM_TIME = "",
                                MANAGE_END_TIME = "",
                                MANAGE_ID = 0,
                                MANAGE_LEVEL = "9",
                                MANAGE_OPERATOR = Enum.KeyWords.外层自动倒库.ToString(),
                                MANAGE_RELATE_CODE = "",
                                MANAGE_REMARK = "",
                                MANAGE_SOURCE = Enum.SystemName.SSWMS.ToString(),
                                MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString(),
                                MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageMove.ToString(),
                                PLAN_ID = 0,
                                PLAN_TYPE_CODE = "",
                                START_CELL_ID = neiStorageMain.CELL_ID,
                                CELL_MODEL = neiStorageMain.CELL_MODEL,
                                END_CELL_ID = 0,
                                PICK_SEQ = "",
                                STOCK_WEIGHT = neiStorageMain.STOCK_WEIGHT,
                                BACKUP_FIELD2 = ""
                            };

                            result = S_Base.sBase.sManage.ManageCreate(
                                mMANAGE_MAIN: neiManageMain,
                                lsMANAGE_LIST: null,
                                raiseTrans: false,
                                checkStorage: true,
                                checkManage: true,
                                checkCellStatus: true,
                                autoComplete: false,
                                autoControl: true,                                
                                doubleInAutoMove: false,
                                out message);

                            if (!result)
                            {
                                message = $"下达[{mMANAGE_MAIN.STOCK_BARCODE}]任务时外层货位倒库任务下达失败_信息[{message}]";
                                return result;
                            }
                        }
                    }
                }

                if (autoControl)
                {
                    result = ManageDownLoad(mMANAGE_MAIN.MANAGE_ID, null, false, out message);
                    if (!result)
                    {
                        return result;
                    }
                }

            }
            catch (Exception ex)
            {
                result = false;
                message = $"生成任务时异常_信息[{ex.Message}]";
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(raiseTrans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(raiseTrans);
                }
            }

            return result;
        }

        /// <summary>
        /// 任务完成
        /// </summary>
        public bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);
            if (mMANAGE_MAIN == null)
            {
                bResult = false;
                sResult = string.Format("未能找到任务信息_任务ID[{0}]", MANAGE_ID);
                return bResult;
            }
            IList<Model.MANAGE_LIST> lsMANAGE_LIST = S_Base.sBase.pMANAGE_LIST.GetListManageID(MANAGE_ID);
            if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count < 1)
            {
                bResult = false;
                sResult = string.Format("未能找到任务列表信息_任务ID[{0}]", MANAGE_ID);
                return bResult;
            }
            Model.MANAGE_TYPE mMANAGE_TYPE = S_Base.sBase.pMANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
            if (mMANAGE_TYPE == null)
            {
                bResult = false;
                sResult = string.Format("未找到任务类型_任务条码[{0}]_类型编码[{1}]", mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.MANAGE_TYPE_CODE);
                return bResult;
            }

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(bTrans);

                Model.WH_CELL mWH_CELL_START = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                Model.WH_CELL mWH_CELL_END = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                if (mWH_CELL_START != null &&
                    (mWH_CELL_START.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_START.CELL_STORAGE_TYPE == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = S_Base.sBase.sManage.CellUpdateStatus(mWH_CELL_START.CELL_ID, Enum.CELL_STATUS.Nohave.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新起始位置[{0}]状态错误", mMANAGE_MAIN.START_CELL_ID.ToString());
                    return bResult;
                }

                if (mWH_CELL_END != null && mWH_CELL_END.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                {
                    if (lsMANAGE_LIST[0].GOODS_ID == 1)
                        bResult = S_Base.sBase.sManage.CellUpdateStatus(mWH_CELL_END.CELL_ID, Enum.CELL_STATUS.Pallet.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);
                    else
                        bResult = S_Base.sBase.sManage.CellUpdateStatus(mWH_CELL_END.CELL_ID, Enum.CELL_STATUS.Full.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新终止位置[{0}]状态错误", mMANAGE_MAIN.END_CELL_ID.ToString());
                    return bResult;
                }

                //库存处理                
                switch ((Enum.MANAGE_TYPE_INOUT)int.Parse(mMANAGE_TYPE.MANAGE_TYPE_INOUT))
                {
                    case Enum.MANAGE_TYPE_INOUT.In:
                        bResult = S_Base.sBase.sSystem.StorageCreate(MANAGE_ID, out sResult);
                        break;

                    case Enum.MANAGE_TYPE_INOUT.Out:
                        bResult = S_Base.sBase.sSystem.StorageDelete(MANAGE_ID, out sResult);
                        break;

                    case Enum.MANAGE_TYPE_INOUT.Move:
                        bResult=S_Base.sBase.sSystem.StorageMove(MANAGE_ID, out sResult); 
                        break;

                    case Enum.MANAGE_TYPE_INOUT.Transport:
                        break;

                    default:
                        bResult = false;
                        sResult = $"任务类型出入库[{mMANAGE_TYPE.MANAGE_TYPE_INOUT}]未定义";
                        break;
                }

                if (!bResult)
                {
                    sResult = string.Format("库存处理错误_{0}", sResult);
                    return bResult;
                }

                //更新完成时间
                if(string.IsNullOrEmpty(mMANAGE_MAIN.MANAGE_END_TIME))
                {
                    mMANAGE_MAIN.MANAGE_END_TIME = Common.StringUtil.GetDateTime();
                    S_Base.sBase.pMANAGE_MAIN.Update(mMANAGE_MAIN);
                }

                //判断计划进度
                if (mMANAGE_MAIN.MANAGE_TYPE_CODE == "ManageIn" || mMANAGE_MAIN.MANAGE_TYPE_CODE == "ManageOut")
                {
                    foreach (var mMANAGE_LIST in lsMANAGE_LIST)
                    {
                        Model.PLAN_LIST mPLAN_LIST = S_Base.sBase.pPLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (mPLAN_LIST != null)
                        {
                            if (mMANAGE_LIST.GOODS_ID == mPLAN_LIST.GOODS_ID)
                            {
                                mPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                            }

                            S_Base.sBase.pPLAN_LIST.Update(mPLAN_LIST);

                            Model.PLAN_MAIN mPLAN_MAIN = S_Base.sBase.pPLAN_MAIN.GetModel(mPLAN_LIST.PLAN_ID);

                            if (mPLAN_MAIN != null)
                            {
                                Model.PLAN_TYPE mPLAN_TYPE = S_Base.sBase.pPLAN_TYPE.GetModelPlanTypeCode(mPLAN_MAIN.PLAN_TYPE_CODE);
                                if (!S_Base.sBase.sPlan.PlanCheckComplete(mPLAN_MAIN.PLAN_ID, out string sPlanComplete))
                                {
                                    bResult = S_Base.sBase.sPlan.PlanComplete(mPLAN_MAIN.PLAN_ID, false, out sPlanComplete);
                                    if (!bResult)
                                    {
                                        sResult = string.Format("计划完成时出错_信息[{0}]", sPlanComplete);
                                        return bResult;
                                    }
                                }
                            }
                        }
                        else
                        {
                            S_Base.sBase.Log.DebugFormat("托盘【{0}】无计划出入库，计划ID【{1}】，计划明细ID【{2}】", mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.PLAN_ID, mMANAGE_LIST.PLAN_LIST_ID);
                        }
                    }
                }

                //处理记录 触发计划更新
                bResult = RecordCreate(mMANAGE_MAIN.MANAGE_ID, out sResult);
                if (!bResult)
                {
                    sResult = string.Format("生成出入库记录错误_{0}", sResult);
                    return bResult;
                }
                
                //调用物资系统接口
                bResult = CallMaterialSystemInterface(mMANAGE_MAIN, lsMANAGE_LIST, mMANAGE_TYPE, out string interfaceMessage);
                if (!bResult)
                {
                    // 接口调用失败不影响任务完成，只记录日志
                    S_Base.sBase.Log.Warn($"物资系统接口调用失败_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_任务类型[{mMANAGE_MAIN.MANAGE_TYPE_CODE}]_信息[{interfaceMessage}]");
                    // 重置结果为成功，继续完成任务
                    bResult = true;
                }



            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("任务完成时发生异常_{0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(bTrans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(bTrans);
                }
            }
            return bResult;
        }

        /// <summary>
        /// 调用物资系统接口
        /// </summary>
        /// <param name="mMANAGE_MAIN">任务主表信息</param>
        /// <param name="lsMANAGE_LIST">任务明细列表</param>
        /// <param name="mMANAGE_TYPE">任务类型信息</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        private bool CallMaterialSystemInterface(Model.MANAGE_MAIN mMANAGE_MAIN, IList<Model.MANAGE_LIST> lsMANAGE_LIST,
            Model.MANAGE_TYPE mMANAGE_TYPE, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 检查是否启用物资系统接口调用
                if (!MaterialSystemInterfaceConfig.IsEnabled())
                {
                    message = "物资系统接口调用已禁用";
                    S_Base.sBase.Log.Debug($"物资系统接口调用已禁用_任务ID[{mMANAGE_MAIN.MANAGE_ID}]");
                    return true; // 禁用时返回成功，不影响任务完成
                }

                // 检查指定任务类型的接口是否启用
                if (!MaterialSystemInterfaceConfig.IsInterfaceEnabled(mMANAGE_TYPE.MANAGE_TYPE_INOUT))
                {
                    message = $"任务类型[{mMANAGE_TYPE.MANAGE_TYPE_INOUT}]的接口调用已禁用";
                    S_Base.sBase.Log.Debug($"任务类型接口调用已禁用_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_类型[{mMANAGE_TYPE.MANAGE_TYPE_INOUT}]");
                    return true; // 禁用时返回成功，不影响任务完成
                }

                // 根据任务类型选择相应的接口调用策略
                var interfaceStrategy = MaterialSystemInterfaceFactory.CreateStrategy(mMANAGE_TYPE.MANAGE_TYPE_INOUT);
                if (interfaceStrategy == null)
                {
                    message = $"不支持的任务类型：{mMANAGE_TYPE.MANAGE_TYPE_INOUT}";
                    S_Base.sBase.Log.Warn($"不支持的任务类型_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_类型[{mMANAGE_TYPE.MANAGE_TYPE_INOUT}]");
                    return true; // 不支持的类型返回成功，不影响任务完成
                }

                // 执行接口调用（支持重试机制）
                int retryCount = MaterialSystemInterfaceConfig.GetRetryCount();
                for (int i = 0; i <= retryCount; i++)
                {
                    result = interfaceStrategy.CallInterface(mMANAGE_MAIN, lsMANAGE_LIST, out message);

                    if (result)
                    {
                        if (i > 0)
                        {
                            S_Base.sBase.Log.Info($"物资系统接口调用重试成功_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_重试次数[{i}]_信息[{message}]");
                        }
                        else
                        {
                            S_Base.sBase.Log.Info($"物资系统接口调用成功_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_任务类型[{mMANAGE_MAIN.MANAGE_TYPE_CODE}]_信息[{message}]");
                        }
                        break;
                    }
                    else if (i < retryCount)
                    {
                        S_Base.sBase.Log.Warn($"物资系统接口调用失败，准备重试_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_重试次数[{i + 1}/{retryCount}]_错误信息[{message}]");
                        System.Threading.Thread.Sleep(1000 * (i + 1)); // 递增延迟重试
                    }
                    else
                    {
                        S_Base.sBase.Log.Error($"物资系统接口调用失败，已达最大重试次数_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_任务类型[{mMANAGE_MAIN.MANAGE_TYPE_CODE}]_信息[{message}]");
                    }
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"物资系统接口调用异常：{ex.Message}";
                S_Base.sBase.Log.Error($"物资系统接口调用异常_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_异常信息[{ex.Message}]", ex);
            }

            return result;
        }

        /// <summary>
        /// 管理-异常
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageException(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;

                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());

                return bResult;
            }

            try
            {
                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);


                S_Base.sBase.sDatabase.BeginTransaction();



                if (mWH_CELL_START != null && mWH_CELL_START.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                {
                    bResult = S_Base.sBase.sManage.CellUpdateStatus(mWH_CELL_START.CELL_ID, Enum.CELL_STATUS.Exception.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新起始位置[{0}]状态错误", mWH_CELL_START.CELL_CODE.ToString());

                        S_Base.sBase.sDatabase.RollBackTransaction();


                        return bResult;
                    }
                }

                if (mWH_CELL_END != null && mWH_CELL_END.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                {
                    bResult = S_Base.sBase.sManage.CellUpdateStatus(mWH_CELL_END.CELL_ID, Enum.CELL_STATUS.Exception.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新目标位置[{0}]状态错误", mWH_CELL_START.CELL_CODE.ToString());

                        S_Base.sBase.sDatabase.RollBackTransaction();


                        return bResult;
                    }
                }


                SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = S_Base.sBase.pIO_CONTROL.GetModelManageID(mMANAGE_MAIN.MANAGE_ID);

                if (null == mIO_CONTROL)
                {
                    sResult = string.Format("未能找到管理任务索引{0}的控制任务", MANAGE_ID.ToString());

                    S_Base.sBase.sDatabase.RollBackTransaction();

                    return bResult;
                }

                mMANAGE_MAIN.MANAGE_REMARK = mIO_CONTROL.ERROR_TEXT;

                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.ExceptionComplete.ToString();

                S_Base.sBase.pMANAGE_MAIN.Update(mMANAGE_MAIN);

                S_Base.sBase.pIO_CONTROL.DeleteManageID(mMANAGE_MAIN.MANAGE_ID);

                S_Base.sBase.sDatabase.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                S_Base.sBase.sDatabase.RollBackTransaction();
            }

            return bResult;
        }

        /// <summary>
        /// 管理-故障
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageError(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);
            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());
                return bResult;
            }

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = S_Base.sBase.pIO_CONTROL.GetModelManageID(mMANAGE_MAIN.MANAGE_ID);

                if (null == mIO_CONTROL)
                {
                    bResult = false;
                    sResult = string.Format("未能找到管理任务索引{0}的控制任务", MANAGE_ID.ToString());
                    S_Base.sBase.sDatabase.RollBackTransaction();
                    return bResult;
                }

                mMANAGE_MAIN.MANAGE_REMARK = mIO_CONTROL.ERROR_TEXT;
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();

                S_Base.sBase.pMANAGE_MAIN.Update(mMANAGE_MAIN);
                S_Base.sBase.sDatabase.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                S_Base.sBase.sDatabase.RollBackTransaction();
            }

            return bResult;
        }

        /// <summary>
        /// 管理-取消
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageCancel(int MANAGE_ID,  out string sResult, bool raiseTrans=true)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);
            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());
                return bResult;
            }
            IList<Model.MANAGE_LIST> lsMANAGE_LIST = S_Base.sBase.pMANAGE_LIST.GetListManageID(MANAGE_ID);
            if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务列表 任务ID{0}", MANAGE_ID.ToString());
                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = S_Base.sBase.pMANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
            if (null == mMANAGE_TYPE)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                return bResult;
            }

            SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = S_Base.sBase.pIO_CONTROL.GetModelManageID(MANAGE_ID);
            if (null != mIO_CONTROL && !(mIO_CONTROL.CONTROL_STATUS ==(int)Enum.CONTROL_STATUS.TaskDelete || mIO_CONTROL.CONTROL_STATUS == (int)Enum.CONTROL_STATUS.Wait))
            {
                bResult = false;
                sResult = string.Format("存在控制任务{0}，先处理控制任务", mIO_CONTROL.CONTROL_ID);
                return bResult;
            }

            //DataTable dtRelateIoControl = S_Base.sBase.sDatabase.GetList(string.Format("select 0 from IO_CONTROL where STOCK_BARCODE ='{0}' and CONTROL_STATUS != 900 ", mMANAGE_MAIN.STOCK_BARCODE));
            //if (dtRelateIoControl.Rows.Count != 0)
            //{
            //    bResult = false;
            //    sResult = string.Format("条码{0}存在控制任务，先处理控制任务", mMANAGE_MAIN.STOCK_BARCODE);
            //    return bResult;
            //}

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(raiseTrans);

                bResult = S_Base.sBase.sManage.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(), out sResult);
                if (!bResult)
                {
                    bResult = false;
                    sResult = string.Format("更新起始货位[{0}]状态错误_{1}", mMANAGE_MAIN.START_CELL_ID.ToString(), sResult);
                    return bResult;
                }

                bResult = S_Base.sBase.sManage.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(), out sResult);
                if (!bResult)
                {
                    bResult = false;
                    sResult = string.Format("更新终止货位[{0}]状态错误_{1}", mMANAGE_MAIN.END_CELL_ID.ToString(), sResult);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = S_Base.sBase.pPLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST = S_Base.sBase.pPLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                    if (null != mPLAN_LIST && mPLAN_LIST.GOODS_ID == mMANAGE_LIST.GOODS_ID)
                    {
                        mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        S_Base.sBase.pPLAN_LIST.Update(mPLAN_LIST);
                    }
                }                

                S_Base.sBase.pIO_CONTROL.DeleteManageID(MANAGE_ID);
                S_Base.sBase.pMANAGE_DETAIL.DeleteManageID(MANAGE_ID);
                S_Base.sBase.pMANAGE_LIST.DeleteManageID(MANAGE_ID);
                S_Base.sBase.pMANAGE_MAIN.Delete(MANAGE_ID);

                //处理空载具任务完成上报接口
                if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.SystemName.iWMS.ToString() &&
                    mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageOut.ToString() &&
                    !string.IsNullOrEmpty(mMANAGE_MAIN.MANAGE_RELATE_CODE) &&
                    mMANAGE_MAIN.MANAGE_RELATE_CODE.StartsWith("CarrierOutRequest"))
                {
                    object[] paramIn = { mMANAGE_MAIN.PLAN_TYPE_CODE,"","","","",Enum.KeyWords.Fail.ToString()};
                    Model.INTERFACE_QUEUE interfaceQueue = new Model.INTERFACE_QUEUE()
                    {
                        QUEUE_ID = 0,
                        TARGET_SYSTEM = Enum.SystemName.iWMS.ToString(),
                        PLAN_ID = mMANAGE_MAIN.PLAN_ID,
                        PLAN_CODE = "",
                        MANAGE_ID = mMANAGE_MAIN.MANAGE_ID,
                        STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE,
                        INTERFACE_NAME = "CarrierOutComplete",
                        INVOKE_TYPE = "",
                        INTERFACE_TYPE = "",
                        HANDLE_FLAG = 0,
                        WRITE_DATETIME = Common.StringUtil.GetDateTime(),
                        PARAM_IN = Common.JsonHelper.Serializer(paramIn)
                    };

                    S_Base.sBase.pINTERFACE_QUEUE.Add(interfaceQueue);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(raiseTrans);
                    S_Base.sBase.Log.InfoFormat("任务取消成功_计划ID[{0}]_任务ID[{1}]_任务类型[{2}]_起始位置ID[{3}]_终点位置ID[{4}]_托盘条码[{5}]", mMANAGE_MAIN.PLAN_ID, mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.MANAGE_TYPE_CODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID, mMANAGE_MAIN.STOCK_BARCODE);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(raiseTrans);
                    S_Base.sBase.Log.WarnFormat("任务取消失败_信息[{0}]_任务ID[{1}]_任务类型[{2}]_起始位置ID[{3}]_终点位置ID[{4}]_托盘条码[{5}]", sResult, mMANAGE_MAIN.PLAN_ID, mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.MANAGE_TYPE_CODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID, mMANAGE_MAIN.STOCK_BARCODE);
                }
            }

            return bResult;
        }        

        /// <summary>
        /// 手动增加申请
        /// </summary>
        public bool ControlApplyAdd(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(bTrans);

                S_Base.sBase.pIO_CONTROL_APPLY.Add(mIO_CONTROL_APPLY);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("写入IO_CONTROL_APPLY异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(bTrans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(bTrans);
                }
            }
            return bResult;
        }

        /// <summary>
        /// 使用web服务方式处理调度申请
        /// </summary>
        public string WsControlApply(string controlApplyType, string deviceCode, string stockBarcode, string controlApplyPara)
        {
            string result = "成功";
            bool bResult = true;
            string message = string.Empty;

            try
            {
                Model.IO_CONTROL_APPLY ioControlApply = new IO_CONTROL_APPLY()
                {
                    APPLY_TASK_STATUS = 0,
                    CONTROL_APPLY_TYPE = controlApplyType,
                    CREATE_TIME = Common.StringUtil.GetDateTime(),
                    DEVICE_CODE = deviceCode,
                    STOCK_BARCODE = stockBarcode,
                    CONTROL_APPLY_PARAMETER = controlApplyPara,
                    //标记是通过Webservice接收的申请
                    CONTROL_APPLY_REMARK = "ScanerReport"
                };

                Model.APPLY_TYPE mAPPLY_TYPE = (Model.APPLY_TYPE)S_Base.sBase.sDatabase.GetModel("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", ioControlApply.CONTROL_APPLY_TYPE).RequestObject;
                bResult = S_Base.sBase.sSystem.DynamicInvoke(mAPPLY_TYPE.APPLY_TYPE_CLASS).ApplyHandle(ioControlApply, out message);
            }
            catch (Exception ex)
            {
                bResult = false;
                message = string.Format("程序发生异常_异常信息[{0}]", ex.Message);
            }
            finally
            {
                if (!bResult)
                {
                    //base.CreateSysLog(Enum.LogThread.Apply, "System", Enum.LOG_LEVEL.Error, string.Format("S_ManageService.WsControlApply():处理Web服务申请失败_{0}", message));
                    result = "失败_" + message;

                    S_Base.sBase.Log.WarnFormat("处理Web服务申请失败_信息[{0}]", message);
                }
            }

            return result;
        }

        /// <summary>
        /// 管理-执行
        /// </summary>
        private bool ManageExecute(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未能找到管理任务_任务ID[{0}]", MANAGE_ID);
                    return bResult;
                }

                mMANAGE_MAIN.MANAGE_REMARK = string.Empty;
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Executing.ToString();

                S_Base.sBase.pMANAGE_MAIN.Update(mMANAGE_MAIN);

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {                    
                    S_Base.sBase.sDatabase.CommitTransaction();
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction();
                }
            }

            return bResult;
        }

        /// <summary>管理-下达 控制是否独立事务
        /// 管理-下达 控制是否独立事务
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool ManageDownLoad(int MANAGE_ID, WH_CELL startCell, bool bTrans, out int controlId, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            controlId = 0;

            int END_CELL_ID = 0;
            SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = null;
            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                return bResult;
            }

            Model.WH_CELL mSTART_CELL;

            if (startCell == null)
            {
                mSTART_CELL = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                if (null == mSTART_CELL)
                {
                    bResult = false;
                    sResult = string.Format("未能找到起始位置_任务起点位置ID[{0}]_任务条码[{1}]", mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }
            }
            else
            {
                mSTART_CELL = startCell;
            }

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(bTrans);

                if (mMANAGE_MAIN.END_CELL_ID.Equals(0))
                {
                    //出库任务 移库任务
                    if (mSTART_CELL.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                    {
                        if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageMove.ToString() &&
                            mMANAGE_MAIN.MANAGE_OPERATOR == Enum.KeyWords.外层自动倒库.ToString())
                        {
                            bResult = CellInAllocate(mMANAGE_MAIN.STOCK_BARCODE.Substring(0,1), mSTART_CELL.CELL_ID, mMANAGE_MAIN.MANAGE_ID, 1, out END_CELL_ID, out sResult);
                            if (!bResult)
                            {
                                sResult = string.Format("倒库分配货位失败_{0}", sResult);
                                return bResult;
                            }
                        }
                        else
                        {
                            bResult = false;
                            sResult = string.Format("未找到终点位置");
                            return bResult;
                        }
                    }
                    //入库任务
                    else
                    {
                        bResult = CellInAllocate(mMANAGE_MAIN.STOCK_BARCODE.Substring(0,1),mSTART_CELL.CELL_ID, mMANAGE_MAIN.MANAGE_ID, 1, out END_CELL_ID, out sResult);
                        if (!bResult)
                        {
                            sResult = string.Format("入库分配货位失败_{0}", sResult);
                            return bResult;
                        }
                    }

                    //mMANAGE_MAIN.START_CELL_ID = mSTART_CELL.CELL_ID;
                    mMANAGE_MAIN.END_CELL_ID = END_CELL_ID;
                }
                else
                {
                    Model.WH_CELL mWH_CELL_END = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);
                    if (mWH_CELL_END == null)
                    {
                        bResult = false;
                        sResult = string.Format("未能获取用户选定的货位 ID{0}_ManageDownLoad", mMANAGE_MAIN.END_CELL_ID);
                        return bResult;
                    }
                }

                SiaSun.LMS.Model.WH_CELL mEND_CELL = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);
                if (null == mEND_CELL)
                {
                    bResult = false;
                    sResult = string.Format("未能找到终止位置_货位ID[{0}]", mMANAGE_MAIN.END_CELL_ID);
                    return bResult;
                }

                //路径校验
                IList<Model.IO_CONTROL_ROUTE> lsIO_CONTROL_ROUTE = S_Base.sBase.pIO_CONTROL_ROUTE.GetList(mSTART_CELL.DEVICE_CODE, mEND_CELL.DEVICE_CODE);
                bResult = lsIO_CONTROL_ROUTE.Count > 0;
                if (!bResult)
                {
                    sResult = string.Format("{0}-{1}之间无可用路径，请在监控系统中查看设备状态！", mSTART_CELL.CELL_CODE, mEND_CELL.CELL_CODE);
                    return bResult;
                }

                Model.WH_WAREHOUSE WH_WAREHOUSE_START = S_Base.sBase.pWH_WAREHOUSE.GetModel(mSTART_CELL.WAREHOUSE_ID);
                Model.WH_WAREHOUSE WH_WAREHOUSE_END = S_Base.sBase.pWH_WAREHOUSE.GetModel(mEND_CELL.WAREHOUSE_ID);
                if (WH_WAREHOUSE_START == null || WH_WAREHOUSE_END == null)
                {
                    bResult = false;
                    sResult = string.Format("未能找到{0}仓库", WH_WAREHOUSE_START == null ? "起始" : "终止");
                    return bResult;
                }

                mIO_CONTROL = S_Base.sBase.pIO_CONTROL.GetModelManageID(MANAGE_ID);
                if (mIO_CONTROL != null)
                {
                    bResult = false;
                    sResult = string.Format("控制任务{0}已经存在!", MANAGE_ID.ToString());
                    return bResult;
                }

                if (S_Base.sBase.pIO_CONTROL.GetList(mMANAGE_MAIN.STOCK_BARCODE).Where(r => r.END_DEVICE_CODE != mSTART_CELL.CELL_CODE).Count() > 0)
                {
                    bResult = false;
                    sResult = string.Format("控制任务已经存在_条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }

                mIO_CONTROL = new SiaSun.LMS.Model.IO_CONTROL();

                mIO_CONTROL.CONTROL_TASK_TYPE = GetControlTaskType(mSTART_CELL.CELL_TYPE, mEND_CELL.CELL_TYPE);
                mIO_CONTROL.RELATIVE_CONTROL_ID = -1;
                mIO_CONTROL.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                mIO_CONTROL.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;
                mIO_CONTROL.CONTROL_TASK_LEVEL = string.IsNullOrEmpty(mMANAGE_MAIN.MANAGE_LEVEL) ? "0" : mMANAGE_MAIN.MANAGE_LEVEL;
                mIO_CONTROL.PRE_CONTROL_STATUS = 0;
                mIO_CONTROL.START_DEVICE_CODE = mSTART_CELL.CELL_CODE;
                mIO_CONTROL.END_DEVICE_CODE = mEND_CELL.CELL_CODE;
                mIO_CONTROL.CONTROL_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                mIO_CONTROL.CONTROL_STATUS = 0;
                mIO_CONTROL.CONTROL_REMARK = mMANAGE_MAIN.MANAGE_REMARK;
                mIO_CONTROL.START_WAREHOUSE_CODE = WH_WAREHOUSE_START.WAREHOUSE_CODE;
                mIO_CONTROL.END_WAREHOUSE_CODE = WH_WAREHOUSE_END.WAREHOUSE_CODE;
                mIO_CONTROL.CELL_GROUP = "0";
                S_Base.sBase.pIO_CONTROL.Add(mIO_CONTROL);
                controlId = mIO_CONTROL.CONTROL_ID;

                mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.Waiting.ToString();

                S_Base.sBase.pMANAGE_MAIN.Update(mMANAGE_MAIN);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(bTrans);

                    S_Base.sBase.Log.InfoFormat("下达关联管理任务的控制任务成功_起始位置[{0}]_终止位置[{1}]_条码[{2}]_CONTROL_ID[{3}]", mIO_CONTROL.START_DEVICE_CODE, mIO_CONTROL.END_DEVICE_CODE, mIO_CONTROL.STOCK_BARCODE, mIO_CONTROL.CONTROL_ID);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(bTrans);

                    S_Base.sBase.Log.WarnFormat("下达关联管理任务的控制任务失败_信息[{0}]_起始位置[{1}]_管理任务ID[{2}]_", sResult, mSTART_CELL.CELL_CODE, MANAGE_ID);
                }
            }

            return bResult;
        }

        /// <summary>
        /// ManageDownLoad重载 不输出controlid
        /// </summary>
        internal bool ManageDownLoad(int MANAGE_ID, Model.WH_CELL startCell, bool bTrans, out string sResult)
        {
            int controlId = 0;
            return ManageDownLoad(MANAGE_ID, startCell, bTrans, out controlId, out sResult);
        }

        /// <summary>
        /// IO_CONTROL上报放货重
        /// </summary>
        private bool ManageReDownloadException(int manageId, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                Model.MANAGE_MAIN manageMain = S_Base.sBase.pMANAGE_MAIN.GetModel(manageId);
                if (manageMain == null)
                {
                    result = false;
                    message = string.Format("未能找到任务_参数manageId[{0}]", manageId);
                    return result;
                }
                //更新任务状态
                manageMain.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();
                manageMain.MANAGE_REMARK = "调度上报放货重复";
                S_Base.sBase.pMANAGE_MAIN.Update(manageMain);

                Model.IO_CONTROL ioControl = S_Base.sBase.pIO_CONTROL.GetModelManageID(manageId);
                if (ioControl == null)
                {
                    result = false;
                    message = string.Format("未能找到控制任务_参数manageId[{0}]", manageId);
                    return result;
                }
                Model.WH_CELL startCell = S_Base.sBase.pWH_CELL.GetModel(manageMain.START_CELL_ID);
                if (startCell == null)
                {
                    result = false;
                    message = string.Format("未能找到起点位置_任务ID[{0}]_条码[{1}]", manageMain.MANAGE_ID, manageMain.STOCK_BARCODE);
                    return result;
                }
                Model.WH_CELL endCell = S_Base.sBase.pWH_CELL.GetModel(manageMain.END_CELL_ID);
                if (endCell == null)
                {
                    result = false;
                    message = string.Format("未能找到终点位置_任务ID[{0}]_条码[{1}]", manageMain.MANAGE_ID, manageMain.STOCK_BARCODE);
                    return result;
                }
                Model.MANAGE_TYPE manageType = S_Base.sBase.pMANAGE_TYPE.GetModelManageTypeCode(manageMain.MANAGE_TYPE_CODE);
                if (manageType == null)
                {
                    result = false;
                    message = string.Format("未能找到任务类型_任务ID[{0}]_条码[{1}]", manageMain.MANAGE_ID, manageMain.STOCK_BARCODE);
                    return result;
                }
                if (!new string[] { "1", "3" }.Contains(manageType.MANAGE_TYPE_INOUT))
                {
                    result = false;
                    message = string.Format("重置入库货位功能只针对入库或者上架任务有效_任务ID[{0}]_条码[{1}]", manageMain.MANAGE_ID, manageMain.STOCK_BARCODE);
                    return result;
                }

                //重新下达IO_CONTROL
                int newEndCellID = 0;
                //result = CellMoveAllocate(startCell.CELL_ID, Enum.CELL_TYPE.Cell, out newEndCellID, out message);
                if (!result || newEndCellID == 0)
                {
                    result = false;
                    message = string.Format("放货重复后重新分配货位失败_信息[{0}]_条码[{1}]", message, manageMain.STOCK_BARCODE);
                    return result;
                }
                Model.WH_CELL newEndCell = S_Base.sBase.pWH_CELL.GetModel(newEndCellID);
                if (newEndCell == null)
                {
                    result = false;
                    message = string.Format("未能找到重新分配的货位_货位ID[{0}]_条码[{1}]", newEndCellID, manageMain.STOCK_BARCODE);
                    return result;
                }
                ioControl.CONTROL_STATUS = int.Parse(Enum.CONTROL_STATUS.LterRouteReplay.ToString("d"));
                ioControl.END_DEVICE_CODE = newEndCell.CELL_CODE;
                S_Base.sBase.pIO_CONTROL.Update(ioControl);

                //将新分配的货位标记为运行
                newEndCell.RUN_STATUS = Enum.RUN_STATUS.Run.ToString();
                S_Base.sBase.pWH_CELL.Update(newEndCell);

                //将终点位置标记为不可用
                endCell.RUN_STATUS = Enum.RUN_STATUS.Disable.ToString();
                S_Base.sBase.pWH_CELL.Update(endCell);

                //重置任务终点位置
                manageMain.END_CELL_ID = newEndCellID;
                S_Base.sBase.pMANAGE_MAIN.Update(manageMain);

                //base.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("ManageBase.ManageReDownloadException():调度上报放货重复_将异常货位标记为禁用_任务ID[{0}]_条码[{1}]_货位编码[{2}]", manageId, manageMain.STOCK_BARCODE, endCell.CELL_CODE));
                S_Base.sBase.Log.InfoFormat("调度上报放货重复处理成功_已重新选定货位_将原位标记为禁用_任务ID[{0}]_条码[{1}]_原货位[{2}]_新选定货位[{3}]", manageId, manageMain.STOCK_BARCODE, endCell.CELL_CODE, newEndCell.CELL_CODE);
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("重置货位时发生异常_任务ID[{0}]_异常信息[{1}]", manageId, ex.Message);
                //base.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Error, string.Format("ManageBase.ManageReDownloadException():重置货位时发生异常_任务ID[{0}]_异常信息[{1}]", manageId,ex.Message));
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction();

                    S_Base.sBase.Log.InfoFormat("处理放货重成功");
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction();

                    S_Base.sBase.Log.WarnFormat("处理放货重成功失败_信息[{0}]", message);
                }
            }

            return result;
        }
        
        /// <summary>
        /// 生成出入库记录
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool RecordCreate(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);
                if (null == mMANAGE_MAIN)
                {
                    bResult = false;
                    sResult = string.Format("任务号{0}不存在", MANAGE_ID);
                    return bResult;
                }

                Model.RECORD_MAIN mRECORD_MAIN = new Model.RECORD_MAIN();

                Model.PLAN_MAIN mPLAN_MAIN = S_Base.sBase.pPLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);

                Model.WH_CELL mSTART_WH_CELL = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                Model.WH_CELL mEND_WH_CELL = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                if (mPLAN_MAIN != null)
                {
                    mRECORD_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                    mRECORD_MAIN.PLAN_CODE = mPLAN_MAIN.PLAN_CODE;
                    mRECORD_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                }
                mRECORD_MAIN.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                mRECORD_MAIN.MANAGE_TYPE_CODE = mMANAGE_MAIN.MANAGE_TYPE_CODE;
                mRECORD_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;

                if (mSTART_WH_CELL != null)
                {
                    mRECORD_MAIN.START_POSITION = mSTART_WH_CELL.CELL_CODE;
                    mRECORD_MAIN.START_POSITION_ID = mSTART_WH_CELL.CELL_ID;
                }

                if (mEND_WH_CELL != null)
                {
                    mRECORD_MAIN.END_POSITION = mEND_WH_CELL.CELL_CODE;
                    mRECORD_MAIN.END_POSITION_ID = mEND_WH_CELL.CELL_ID;
                }

                mRECORD_MAIN.RECORD_OPERATOR = mMANAGE_MAIN.MANAGE_OPERATOR;
                mRECORD_MAIN.MANAGE_BEGIN_TIME = mMANAGE_MAIN.MANAGE_BEGIN_TIME;
                mRECORD_MAIN.MANAGE_END_TIME = mMANAGE_MAIN.MANAGE_END_TIME;
                mRECORD_MAIN.RECORD_REMARK = mMANAGE_MAIN.MANAGE_REMARK;
                mRECORD_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;
                mRECORD_MAIN.MANAGE_SOURCE = mMANAGE_MAIN.MANAGE_SOURCE;
                mRECORD_MAIN.MANAGE_RELATE_CODE = mMANAGE_MAIN.MANAGE_RELATE_CODE;
                mRECORD_MAIN.MANAGE_CONFIRM_TIME = Common.StringUtil.GetDateTime();

                mRECORD_MAIN.STOCK_WEIGHT = mMANAGE_MAIN.STOCK_WEIGHT;

                S_Base.sBase.pRECORD_MAIN.Add(mRECORD_MAIN);

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = S_Base.sBase.pMANAGE_LIST.GetListManageID(MANAGE_ID);

                foreach (Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    Model.RECORD_LIST mRECORD_LIST = new Model.RECORD_LIST();

                    mRECORD_LIST.RECORD_ID = mRECORD_MAIN.RECORD_ID;
                    mRECORD_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;
                    mRECORD_LIST.RECORD_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                    mRECORD_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;
                    mRECORD_LIST.RECORD_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;
                    mRECORD_LIST.BOX_BARCODE = mMANAGE_LIST.BOX_BARCODE;

                    bResult = S_Base.sBase.sSystem.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mRECORD_LIST, mMANAGE_LIST, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    S_Base.sBase.pRECORD_LIST.Add(mRECORD_LIST);

                    IList<Model.MANAGE_DETAIL> lsMANAGE_DETAIL = S_Base.sBase.pMANAGE_DETAIL.GetListManageListID(mMANAGE_LIST.MANAGE_LIST_ID);

                    foreach (Model.MANAGE_DETAIL mMANAGE_DETAIL in lsMANAGE_DETAIL)
                    {
                        Model.RECORD_DETAIL mRECORD_DETAIL = new Model.RECORD_DETAIL();

                        mRECORD_DETAIL.RECORD_LIST_ID = mRECORD_LIST.RECORD_LIST_ID;
                        mRECORD_DETAIL.BOX_BARCODE = mMANAGE_DETAIL.BOX_BARCODE;
                        mRECORD_DETAIL.GOODS_BARCODE = mMANAGE_DETAIL.GOODS_BARCODE;
                        mRECORD_DETAIL.RECORD_DETAIL_REMARK = mMANAGE_DETAIL.MANAGE_DETAIL_REMARK;

                        S_Base.sBase.pRECORD_DETAIL.Add(mRECORD_DETAIL);
                    }
                }

                S_Base.sBase.pIO_CONTROL.DeleteManageID(MANAGE_ID);
                S_Base.sBase.pMANAGE_DETAIL.DeleteManageID(MANAGE_ID);
                S_Base.sBase.pMANAGE_LIST.DeleteManageID(MANAGE_ID);
                S_Base.sBase.pMANAGE_MAIN.Delete(MANAGE_ID);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }

        /// <summary>
        /// 获得入库货位
        /// </summary>
        private bool CellInAllocate(string index, int startCellId, int manageId, int forkCount, out int endCellId, out string message)
        {
            bool result = true;
            message = string.Empty;
            string sql = string.Empty;
            string extra = string.Empty;
            string device_code = string.Empty;
            endCellId = 0;

            try
            {
                if (string.IsNullOrEmpty(index))
                {
                    result = false;
                    message = $"未找到托盘规格标识!";
                    return result;
                }

                Model.WH_CELL startCell = S_Base.sBase.pWH_CELL.GetModel(startCellId);
                if (startCell == null)
                {
                    result = false;
                    message = $"未找到ID为[{startCellId}]的起始货位";
                    return result;
                }

                Model.MANAGE_MAIN manageMain = S_Base.sBase.pMANAGE_MAIN.GetModel(manageId);
                if (manageMain == null)
                {
                    result = false;
                    message = $"未找到ID为[{manageId}]的任务信息";
                    return result;
                }

                IList<Model.MANAGE_LIST> manageLists = S_Base.sBase.pMANAGE_LIST.GetListManageID(manageId);
                if (manageLists == null || manageLists.Count == 0)
                {
                    result = false;
                    message = $"未能获取任务列表_任务ID[{manageId}]";
                    return result;
                }

                switch (startCell.CELL_CODE)
                {
                    case "12001":
                    case "12002":
                        device_code = "18001";
                        break;
                    case "12003":
                    case "12004":
                        device_code = "18002";
                        break;
                    case "12005":
                    case "12006":
                        device_code = "18003";
                        break;
                    case "12007":
                    case "12008":
                        device_code = "18004";
                        break;
                    default:
                        result = false;
                        message = $"未能获取巷道信息";
                        return result;
                }
                switch (index)
                {
                    case "A":
                    case "C":
                    case "E":
                    case "G":
                        extra = " and CELL_Y in (2,4,6,8)";
                        break;
                    case "B":
                    case "D":
                    case "F":
                    case "H":
                        extra = " and CELL_Y in (1,3,5,7)";
                        break;
                }

                sql = $"select cell_id, cell_code from WH_CELL where device_code = '{device_code}' and CELL_STATUS = 'Nohave' and RUN_STATUS = 'Enable' and CELL_TYPE = 'Cell' and CELL_ID not in (select CELL_ID from STORAGE_MAIN) {extra} order by cell_z, cell_y, cell_x asc";

                DataTable dtGetCell = S_Base.sBase.sDatabase.GetList(sql);

                if(dtGetCell.Rows.Count > 0)
                {
                    endCellId = Convert.ToInt32(dtGetCell.Rows[0]["CELL_ID"]);
                }
                else if (index.Equals("A") || index.Equals("C") || index.Equals("E") || index.Equals("G"))
                {
                    extra = " and CELL_Y in (1,3,5,7)";
                    sql = $"select cell_id, cell_code from WH_CELL where device_code = '{device_code}' and CELL_STATUS = 'Nohave' and RUN_STATUS = 'Enable' and CELL_TYPE = 'Cell' and CELL_ID not in (select CELL_ID from STORAGE_MAIN) {extra} order by cell_z, cell_y, cell_x asc";
                    dtGetCell = S_Base.sBase.sDatabase.GetList(sql);
                    if(dtGetCell.Rows.Count > 0)
                    {
                        endCellId = Convert.ToInt32(dtGetCell.Rows[0]["CELL_ID"]);
                    }
                    else
                    {
                        result = false;
                        message = $"当前{startCell.DEVICE_CODE}巷道没有可用货位，请检查或等待";
                        return result;
                    }
                }
                else
                {
                    result = false;
                    message = $"当前{startCell.DEVICE_CODE}巷道没有可用货位，请检查或等待";
                    return result;
                }

                //更新货位状态
                result = S_Base.sBase.sManage.CellUpdateStatus(endCellId, string.Empty, Enum.RUN_STATUS.Selected.ToString(), out message, "");
                if (!result)
                {
                    message = $"更新货位状态错误_{message}";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("分配货位异常_{0}", ex.Message);
            }


            return result;
        }
                
        /// <summary>货位-更改状态
        /// 货位-更改状态
        /// </summary>
        internal bool CellUpdateStatus(int CELL_ID, string CELL_STATUS, string RUN_STATUS, out string sResult, string lockString = "")
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                if (0 == CELL_ID)
                {
                    return bResult;
                }

                SiaSun.LMS.Model.WH_CELL mWH_CELL = S_Base.sBase.pWH_CELL.GetModel(CELL_ID);
                if (mWH_CELL == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到货位ID[{0}]", CELL_ID.ToString());
                    return bResult;
                }

                if (mWH_CELL.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL.CELL_TYPE == Enum.CELL_TYPE.Tank.ToString())
                {
                    var oldCellStatus = mWH_CELL.CELL_STATUS;
                    var oldRunStatus = mWH_CELL.RUN_STATUS;

                    if (!string.IsNullOrEmpty(CELL_STATUS))
                    {
                        mWH_CELL.CELL_STATUS = CELL_STATUS;
                    }
                    if (!string.IsNullOrEmpty(RUN_STATUS))
                    {
                        mWH_CELL.RUN_STATUS = RUN_STATUS;
                    }

                    int refCount = S_Base.sBase.pWH_CELL.Update(mWH_CELL);

                    S_Base.sBase.Log.Info($"更新货位[{mWH_CELL.AREA_ID}|{mWH_CELL.CELL_CODE}]状态[{oldCellStatus}][{oldRunStatus}]->[{mWH_CELL.CELL_STATUS}][{mWH_CELL.RUN_STATUS}]_影响行数[{refCount}]_调用方法[{new StackTrace().GetFrame(2).GetMethod().Name}]");

                    if(refCount < 1)
                    {
                        refCount = S_Base.sBase.sDatabase.ExecuteNonQuery($"update WH_CELL set CELL_STATUS='{CELL_STATUS}' , RUN_STATUS='{RUN_STATUS}' where CELL_ID={CELL_ID}");
                        S_Base.sBase.Log.Info($"再次更新货位[{mWH_CELL.AREA_ID}|{mWH_CELL.CELL_CODE}]状态为[{CELL_STATUS}][{RUN_STATUS}]_影响行数[{refCount}]_调用方法[{new StackTrace().GetFrame(2).GetMethod().Name}]");
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }

        /// <summary>
        /// 生成不关联管理任务的Control任务_wdz
        /// </summary>
        internal bool ControlCreate(Model.IO_CONTROL ioControl, out string message, bool verifyRoute = true, bool wsNoticeControl = false)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                //判断是否存在起点相同的控制任务
                IList<Model.IO_CONTROL> lsIO_CONTROL_CHECK = S_Base.sBase.pIO_CONTROL.GetList(ioControl.STOCK_BARCODE);
                if (lsIO_CONTROL_CHECK != null && lsIO_CONTROL_CHECK.Count > 0 &&
                    lsIO_CONTROL_CHECK.Where(r => r.START_DEVICE_CODE == ioControl.START_DEVICE_CODE && r.START_WAREHOUSE_CODE == ioControl.START_WAREHOUSE_CODE).Count() > 0)
                {
                    result = false;
                    message = string.Format("控制任务已经存在_条码[{0}]", ioControl.STOCK_BARCODE);
                    return result;
                }

                //验证路径            
                if (verifyRoute &&
                    !string.IsNullOrEmpty(ioControl.START_DEVICE_CODE) && !string.IsNullOrEmpty(ioControl.END_DEVICE_CODE))
                {
                    DataTable dtIoControlRouteEnable = S_Base.sBase.sDatabase.GetList(string.Format("select * from IO_CONTROL_ROUTE where START_DEVICE = '{0}' and END_DEVICE = '{1}' and CONTROL_ROUTE_STATUS = '1' and CONTROL_ROUTE_MANAGE = '1' ", ioControl.START_DEVICE_CODE, ioControl.END_DEVICE_CODE));
                    if (dtIoControlRouteEnable == null || dtIoControlRouteEnable.Rows.Count < 1)
                    {
                        result = false;
                        message = string.Format("任务路径不可用_起点[{0}]_终点[{1}]", ioControl.START_DEVICE_CODE, ioControl.END_DEVICE_CODE);
                        return result;
                    }
                }

                S_Base.sBase.pIO_CONTROL.Add(ioControl);
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("下达Control时发生异常 {0}", ex.Message);

                S_Base.sBase.Log.Error("下达Control时发生异常", ex);
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.Log.InfoFormat("下达非关联管理任务的Control任务成功_条码[{0}]_起点[{1}]_终点[{2}]", ioControl.STOCK_BARCODE, ioControl.START_DEVICE_CODE, ioControl.END_DEVICE_CODE);
                }
                else
                {
                    S_Base.sBase.Log.ErrorFormat("下达非关联管理任务的Control任务失败_条码[{0}]_起点[{1}]_终点[{2}]_信息[{3}]", ioControl.STOCK_BARCODE, ioControl.START_DEVICE_CODE, ioControl.END_DEVICE_CODE, message);
                }
            }

            return result;
        }

        /// <summary>
        /// 根据控制任务起点和终点判断任务类型
        /// 2020-04-03 14:25
        /// </summary>
        /// <param name="startCellType"></param>
        /// <param name="endCellType"></param>
        /// <returns></returns>
        internal int GetControlTaskType(string startCellType, string endCellType)
        {
            int taskType = 0;

            string inOutType = string.Format("{0}-{1}", startCellType, endCellType).ToLower();

            switch (inOutType)
            {
                //站台-货位-入
                case "station-cell":
                    taskType = (int)Enum.CONTROL_TYPE.In;
                    break;

                //货位-站台-出
                case "cell-station":
                    taskType = (int)Enum.CONTROL_TYPE.Out;
                    break;

                //货位-货位-移
                case "cell-cell":
                    taskType = (int)Enum.CONTROL_TYPE.Move;
                    break;

                //站台-站台-移
                case "station-station":
                    taskType = (int)Enum.CONTROL_TYPE.MoveStation;
                    break;
            }

            return taskType;
        }

        /// <summary>
        /// 处理控制任务更新状态 2020-02-14 14:22
        /// </summary>
        public void HandleControlTask()
        {
            bool result = true;
            string message = string.Empty;

            IList<Model.IO_CONTROL> lsIO_CONTROL = S_Base.sBase.pIO_CONTROL.GetListChanged();

            foreach (var item in lsIO_CONTROL)
            {
                //清除非关联任务
                if (item.MANAGE_ID == 0 && 
                        (item.CONTROL_STATUS == (int)Enum.CONTROL_STATUS.Finish || 
                         item.CONTROL_STATUS == (int)Enum.CONTROL_STATUS.TaskAbend || 
                         item.CONTROL_STATUS == (int)Enum.CONTROL_STATUS.TaskDelete))
                {
                    S_Base.sBase.Log.InfoFormat("删除非关联管理任务的Control任务_ControlID[{0}]_条码[{1}]_起点[{2}]_终点[{3}]_状态[{4}]", item.CONTROL_ID, item.STOCK_BARCODE, item.START_DEVICE_CODE, item.END_DEVICE_CODE, item.CONTROL_STATUS);
                    S_Base.sBase.pIO_CONTROL.Delete(item.CONTROL_ID);
                    continue;
                }

                if (item.MANAGE_ID > 0)
                {
                    //处理关联管理任务的控制任务
                    switch (item.CONTROL_STATUS)
                    {
                        case 10://堆垛机运行
                            result = ManageExecute(item.MANAGE_ID, out message);
                            break;

                        case 900:
                            result = ManageCancel(item.MANAGE_ID, out message);
                            break;

                        case 980:
                            result = ManageError(item.MANAGE_ID, out message);
                            break;

                        case 999:
                            result = ManageComplete(item.MANAGE_ID, true, out message);
                            break;

                        default:
                            break;
                    }

                    //调度状态变化 打日志
                    if (!result)
                    {
                        S_Base.sBase.Log.WarnFormat("根据Control状态[{0}]处理Manage任务失败_失败信息[{1}]_条码[{4}]_ControlId[{2}]_ManageId[{3}]", item.CONTROL_STATUS, message, item.CONTROL_ID, item.MANAGE_ID, item.STOCK_BARCODE);
                    }
                    else if (item.CONTROL_STATUS != 10 && item.CONTROL_STATUS != 11)
                    {
                        S_Base.sBase.Log.InfoFormat("根据Control状态[{0}]处理Manage任务成功_条码[{1}]_ControlId[{2}]_ManageId[{3}]", item.CONTROL_STATUS, item.STOCK_BARCODE, item.CONTROL_ID, item.MANAGE_ID);
                    }

                    if (!result)
                    {
                        //将错误信息更新给MANAGE
                        Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(item.MANAGE_ID);
                        if (mMANAGE_MAIN != null)
                        {
                            mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();
                            mMANAGE_MAIN.MANAGE_REMARK = message + string.Format("_控制任务状态[{0}]", item.CONTROL_STATUS);
                            S_Base.sBase.pMANAGE_MAIN.Update(mMANAGE_MAIN);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 处理申请方法
        /// </summary>
        public void HandleControlApply()
        {
            string sResult = string.Empty;

            IList<SiaSun.LMS.Model.IO_CONTROL_APPLY> lsIO_CONTROL_APPLY = S_Base.sBase.pIO_CONTROL_APPLY.GetList(0);

            foreach (SiaSun.LMS.Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY in lsIO_CONTROL_APPLY)
            {
                S_Base.sBase.Log.Info($"进入申请ID[{mIO_CONTROL_APPLY.CONTROL_APPLY_ID}]_申请信息[{Common.JsonHelper.Serializer(mIO_CONTROL_APPLY)}]");

                Model.APPLY_TYPE mAPPLY_TYPE;
                if (mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE == "0")
                {
                    //按申请设备
                    mAPPLY_TYPE = S_Base.sBase.pAPPLY_TYPE.GetModelDeviceCode(mIO_CONTROL_APPLY.DEVICE_CODE);
                }
                else
                {
                    //按申请类型
                    mAPPLY_TYPE = S_Base.sBase.pAPPLY_TYPE.GetModelApplyTypeCode(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE);
                }
                if (mAPPLY_TYPE == null)
                {
                    Model.IO_CONTROL_APPLY_HIS mIO_CONTROL_APPLY_HIS = new Model.IO_CONTROL_APPLY_HIS()
                    {
                        APPLY_TASK_STATUS = 0,
                        CONTROL_APPLY_PARA01 = mIO_CONTROL_APPLY.CONTROL_APPLY_PARA01,
                        CONTROL_APPLY_PARA02 = mIO_CONTROL_APPLY.CONTROL_APPLY_PARA02,
                        CONTROL_APPLY_PARAMETER = mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER,
                        CONTROL_APPLY_REMARK = mIO_CONTROL_APPLY.CONTROL_APPLY_REMARK,
                        CONTROL_APPLY_TYPE = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE,
                        CONTROL_ERROR_TEXT = mIO_CONTROL_APPLY.CONTROL_ERROR_TEXT,
                        CONTROL_ID = mIO_CONTROL_APPLY.CONTROL_ID,
                        CREATE_TIME = mIO_CONTROL_APPLY.CREATE_TIME,
                        DEVICE_CODE = mIO_CONTROL_APPLY.DEVICE_CODE,
                        HANDLE_TIME = Common.StringUtil.GetDateTime(),
                        MANAGE_ERROR_TEXT = "处理申请时未能获取本次申请的申请类型实例",
                        STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE,
                        WAREHOUSE_CODE = mIO_CONTROL_APPLY.WAREHOUSE_CODE
                    };

                    S_Base.sBase.pIO_CONTROL_APPLY_HIS.Add(mIO_CONTROL_APPLY_HIS);
                    S_Base.sBase.pIO_CONTROL_APPLY.Delete(mIO_CONTROL_APPLY.CONTROL_APPLY_ID);

                    break;
                }

                mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE = mAPPLY_TYPE.APPLY_TYPE_CODE;

                bool result = S_Base.sBase.sSystem.DynamicInvoke(mAPPLY_TYPE.APPLY_TYPE_CLASS).ApplyHandle(mIO_CONTROL_APPLY, out sResult);

                S_Base.sBase.Log.Info($"退出申请ID[{mIO_CONTROL_APPLY.CONTROL_APPLY_ID}]_返回结果[{(result?"成功":$"失败_信息[{sResult}]")}]");
            }
        }

        public bool GoodsCreate(DataTable dt, int GOODS_CLASS_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            int rowaffect = 0;
            Model.GOODS_CLASS mGOODS_CLASS = null;

            try
            {
                if (GOODS_CLASS_ID != 0)
                {
                    mGOODS_CLASS = S_Base.sBase.pGOODS_CLASS.GetModel(GOODS_CLASS_ID);

                    if (mGOODS_CLASS == null)
                    {
                        sResult = string.Format("物料类别索引[{0}]不存在", GOODS_CLASS_ID);
                        bResult = false;
                        return bResult;
                    }
                }

                foreach (DataRow dr in dt.Rows)
                {
                    //[GOODS_CODE]-材料编码    [GOODS_NAME]-物资名称
                    if (string.IsNullOrEmpty(dr["物料编码"].ToString()) || string.IsNullOrEmpty(dr["物料名称"].ToString()))
                        continue;

                    Model.GOODS_MAIN mGOODS_MAIN = S_Base.sBase.pGOODS_MAIN.GetModel(dr["物料编码"].ToString());

                    if (mGOODS_MAIN == null)
                    {
                        //新增物料信息
                        mGOODS_MAIN = new Model.GOODS_MAIN();
                        mGOODS_MAIN.GOODS_CLASS_ID = 1;//
                        mGOODS_MAIN.GOODS_CODE = dr["物料编码"].ToString();
                        mGOODS_MAIN.GOODS_NAME = dr["物料名称"].ToString();
                        mGOODS_MAIN.GOODS_UNITS = dr["单位"].ToString();

                        mGOODS_MAIN.GOODS_CONST_PROPERTY1 = dr["备用属性1"].ToString();
                        mGOODS_MAIN.GOODS_CONST_PROPERTY2 = dr["备用属性2"].ToString();
                        mGOODS_MAIN.GOODS_CONST_PROPERTY3 = dr["备用属性3"].ToString();
                        mGOODS_MAIN.GOODS_REMARK = dr["备注"].ToString();

                        mGOODS_MAIN.GOODS_FLAG = "1";
                        rowaffect += S_Base.sBase.pGOODS_MAIN.Add(mGOODS_MAIN);
                    }
                    else
                    {
                        //修改物料信息                   
                        mGOODS_MAIN.GOODS_CLASS_ID = 1;//
                        mGOODS_MAIN.GOODS_CODE = dr["物料编码"].ToString();
                        mGOODS_MAIN.GOODS_NAME = dr["物料名称"].ToString();
                        mGOODS_MAIN.GOODS_UNITS = dr["单位"].ToString();

                        mGOODS_MAIN.GOODS_CONST_PROPERTY1 = dr["备用属性1"].ToString();
                        mGOODS_MAIN.GOODS_CONST_PROPERTY2 = dr["备用属性2"].ToString();
                        mGOODS_MAIN.GOODS_CONST_PROPERTY3 = dr["备用属性3"].ToString();
                        mGOODS_MAIN.GOODS_REMARK = dr["备注"].ToString();

                        mGOODS_MAIN.GOODS_FLAG = "1";
                        rowaffect += S_Base.sBase.pGOODS_MAIN.Update(mGOODS_MAIN);
                    }
                }

                if (rowaffect > 0)
                {
                    sResult = string.Format("导入{0}条物料成功!", rowaffect);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }

        

    }
}