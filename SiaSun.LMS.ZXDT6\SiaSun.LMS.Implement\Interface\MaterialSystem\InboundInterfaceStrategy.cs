using System;
using System.Collections.Generic;
using System.Linq;
using SiaSun.LMS.Model;
using SiaSun.LMS.Implement.Interface.iWMS;

namespace SiaSun.LMS.Implement.Interface.MaterialSystem
{
    /// <summary>
    /// 入库任务物资系统接口调用策略
    /// </summary>
    public class InboundInterfaceStrategy : IMaterialSystemInterfaceStrategy
    {
        public string StrategyName => "入库任务接口策略";

        /// <summary>
        /// 调用入库完成回调接口
        /// </summary>
        /// <param name="manageMain">任务主表信息</param>
        /// <param name="manageList">任务明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool CallInterface(MANAGE_MAIN manageMain, IList<MANAGE_LIST> manageList, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 参数校验
                if (manageMain == null)
                {
                    result = false;
                    message = "任务主表信息不能为空";
                    return result;
                }

                if (manageList == null || manageList.Count == 0)
                {
                    result = false;
                    message = "任务明细列表不能为空";
                    return result;
                }

                // 获取货位信息
                var endCell = S_Base.sBase.pWH_CELL.GetModel(manageMain.END_CELL_ID);
                if (endCell == null)
                {
                    result = false;
                    message = $"未找到终点货位信息_货位ID[{manageMain.END_CELL_ID}]";
                    return result;
                }

                // 获取仓库信息
                var area = S_Base.sBase.pWH_AREA.GetModel(endCell.AREA_ID);
                if (area == null)
                {
                    result = false;
                    message = $"未找到货位所属区域信息_区域ID[{endCell.AREA_ID}]";
                    return result;
                }

                // 构建入库完成明细列表
                var inboundItems = new List<InboundReceiptCompleteCallback.InboundReceiptCompleteItem>();

                foreach (var manageItem in manageList)
                {
                    // 获取计划明细信息（用于获取原始ID）
                    var planList = S_Base.sBase.pPLAN_LIST.GetModel(manageItem.PLAN_LIST_ID);
                    
                    var inboundItem = new InboundReceiptCompleteCallback.InboundReceiptCompleteItem
                    {
                        storageNum = (int)manageItem.MANAGE_LIST_QUANTITY,
                        warehouseCode = area.AREA_CODE,
                        shelfCode = endCell.CELL_CODE,
                        oId = planList?.PLAN_LIST_CODE ?? manageItem.MANAGE_LIST_ID.ToString(), // 原始明细ID
                        lId = manageItem.MANAGE_LIST_ID.ToString(), // 立体仓系统明细ID
                        storageType = GetStorageType(manageMain.MANAGE_TYPE_CODE) // 入库类型
                    };

                    inboundItems.Add(inboundItem);
                }

                // 调用入库完成回调接口
                var callback = new InboundReceiptCompleteCallback();
                result = callback.IntefaceMethod(inboundItems, out message);

                if (result)
                {
                    S_Base.sBase.Log.Info($"入库完成接口调用成功_任务ID[{manageMain.MANAGE_ID}]_托盘条码[{manageMain.STOCK_BARCODE}]_明细数量[{inboundItems.Count}]");
                }
                else
                {
                    S_Base.sBase.Log.Error($"入库完成接口调用失败_任务ID[{manageMain.MANAGE_ID}]_托盘条码[{manageMain.STOCK_BARCODE}]_错误信息[{message}]");
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"入库完成接口调用异常：{ex.Message}";
                S_Base.sBase.Log.Error($"入库完成接口调用异常_任务ID[{manageMain?.MANAGE_ID}]_异常信息[{ex.Message}]", ex);
            }

            return result;
        }

        /// <summary>
        /// 根据任务类型获取入库类型
        /// </summary>
        /// <param name="manageTypeCode">任务类型编码</param>
        /// <returns>入库类型：28=入库单，73=出库红冲单，75=归还单</returns>
        private int GetStorageType(string manageTypeCode)
        {
            switch (manageTypeCode)
            {
                case "ManageIn":
                    return 28; // 入库单
                case "ManageAdjustIn":
                    return 73; // 出库红冲单（盘盈入库）
                default:
                    return 28; // 默认为入库单
            }
        }
    }
}
