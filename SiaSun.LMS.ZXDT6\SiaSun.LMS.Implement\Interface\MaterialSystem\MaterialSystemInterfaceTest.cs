using System;
using System.Collections.Generic;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement.Interface.MaterialSystem
{
    /// <summary>
    /// 物资系统接口集成测试类
    /// </summary>
    public class MaterialSystemInterfaceTest
    {
        /// <summary>
        /// 测试入库任务接口调用
        /// </summary>
        public static void TestInboundInterface()
        {
            try
            {
                Console.WriteLine("=== 测试入库任务接口调用 ===");

                // 创建模拟的入库任务数据
                var manageMain = CreateMockInboundTask();
                var manageList = CreateMockManageList(manageMain.MANAGE_ID);

                // 获取入库接口策略
                var strategy = MaterialSystemInterfaceFactory.CreateStrategy("1"); // 1=入库
                if (strategy == null)
                {
                    Console.WriteLine("✗ 创建入库接口策略失败");
                    return;
                }

                Console.WriteLine($"✓ 创建接口策略成功：{strategy.StrategyName}");

                // 调用接口
                bool result = strategy.CallInterface(manageMain, manageList, out string message);

                if (result)
                {
                    Console.WriteLine($"✓ 入库接口调用成功：{message}");
                }
                else
                {
                    Console.WriteLine($"✗ 入库接口调用失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试入库接口异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 测试出库任务接口调用
        /// </summary>
        public static void TestOutboundInterface()
        {
            try
            {
                Console.WriteLine("\n=== 测试出库任务接口调用 ===");

                // 创建模拟的出库任务数据
                var manageMain = CreateMockOutboundTask();
                var manageList = CreateMockManageList(manageMain.MANAGE_ID);

                // 获取出库接口策略
                var strategy = MaterialSystemInterfaceFactory.CreateStrategy("2"); // 2=出库
                if (strategy == null)
                {
                    Console.WriteLine("✗ 创建出库接口策略失败");
                    return;
                }

                Console.WriteLine($"✓ 创建接口策略成功：{strategy.StrategyName}");

                // 调用接口
                bool result = strategy.CallInterface(manageMain, manageList, out string message);

                if (result)
                {
                    Console.WriteLine($"✓ 出库接口调用成功：{message}");
                }
                else
                {
                    Console.WriteLine($"✗ 出库接口调用失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试出库接口异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 测试移库任务接口调用
        /// </summary>
        public static void TestTransferInterface()
        {
            try
            {
                Console.WriteLine("\n=== 测试移库任务接口调用 ===");

                // 创建模拟的移库任务数据
                var manageMain = CreateMockTransferTask();
                var manageList = CreateMockManageList(manageMain.MANAGE_ID);

                // 获取移库接口策略
                var strategy = MaterialSystemInterfaceFactory.CreateStrategy("3"); // 3=移库
                if (strategy == null)
                {
                    Console.WriteLine("✗ 创建移库接口策略失败");
                    return;
                }

                Console.WriteLine($"✓ 创建接口策略成功：{strategy.StrategyName}");

                // 调用接口
                bool result = strategy.CallInterface(manageMain, manageList, out string message);

                if (result)
                {
                    Console.WriteLine($"✓ 移库接口调用成功：{message}");
                }
                else
                {
                    Console.WriteLine($"✗ 移库接口调用失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试移库接口异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 测试工厂模式
        /// </summary>
        public static void TestFactory()
        {
            Console.WriteLine("\n=== 测试工厂模式 ===");

            // 测试支持的任务类型
            var supportedTypes = MaterialSystemInterfaceFactory.GetSupportedTaskTypes();
            Console.WriteLine($"支持的任务类型：{string.Join(", ", supportedTypes)}");

            // 测试创建各种策略
            foreach (var type in supportedTypes)
            {
                var strategy = MaterialSystemInterfaceFactory.CreateStrategy(type);
                if (strategy != null)
                {
                    Console.WriteLine($"✓ 任务类型 {type} -> {strategy.StrategyName}");
                }
                else
                {
                    Console.WriteLine($"✗ 任务类型 {type} -> 创建失败");
                }
            }

            // 测试不支持的类型
            var unsupportedStrategy = MaterialSystemInterfaceFactory.CreateStrategy("999");
            Console.WriteLine($"不支持的类型 999 -> {(unsupportedStrategy == null ? "null（正确）" : "不应该创建成功")}");
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始物资系统接口集成测试...\n");

            TestFactory();
            TestInboundInterface();
            TestOutboundInterface();
            TestTransferInterface();

            Console.WriteLine("\n物资系统接口集成测试完成。");
        }

        #region 模拟数据创建方法

        private static MANAGE_MAIN CreateMockInboundTask()
        {
            return new MANAGE_MAIN
            {
                MANAGE_ID = 1001,
                MANAGE_TYPE_CODE = "ManageIn",
                STOCK_BARCODE = "TEST_PALLET_001",
                START_CELL_ID = 1001,
                END_CELL_ID = 2001,
                PLAN_ID = 101
            };
        }

        private static MANAGE_MAIN CreateMockOutboundTask()
        {
            return new MANAGE_MAIN
            {
                MANAGE_ID = 1002,
                MANAGE_TYPE_CODE = "ManageOut",
                STOCK_BARCODE = "TEST_PALLET_002",
                START_CELL_ID = 2001,
                END_CELL_ID = 1001,
                PLAN_ID = 102
            };
        }

        private static MANAGE_MAIN CreateMockTransferTask()
        {
            return new MANAGE_MAIN
            {
                MANAGE_ID = 1003,
                MANAGE_TYPE_CODE = "ManageMove",
                STOCK_BARCODE = "TEST_PALLET_003",
                START_CELL_ID = 2001,
                END_CELL_ID = 2002,
                PLAN_ID = 103
            };
        }

        private static IList<MANAGE_LIST> CreateMockManageList(int manageId)
        {
            return new List<MANAGE_LIST>
            {
                new MANAGE_LIST
                {
                    MANAGE_LIST_ID = manageId * 10 + 1,
                    MANAGE_ID = manageId,
                    GOODS_ID = 1001,
                    MANAGE_LIST_QUANTITY = 100,
                    PLAN_LIST_ID = manageId * 10 + 1,
                    BOX_BARCODE = $"BOX_{manageId}_001"
                },
                new MANAGE_LIST
                {
                    MANAGE_LIST_ID = manageId * 10 + 2,
                    MANAGE_ID = manageId,
                    GOODS_ID = 1002,
                    MANAGE_LIST_QUANTITY = 50,
                    PLAN_LIST_ID = manageId * 10 + 2,
                    BOX_BARCODE = $"BOX_{manageId}_002"
                }
            };
        }

        #endregion
    }
}
