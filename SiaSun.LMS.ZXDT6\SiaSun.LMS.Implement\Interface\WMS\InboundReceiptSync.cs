using SiaSun.LMS.Common;
using SiaSun.LMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 入库单接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class InboundReceiptSync : InterfaceBase
    {
        class InputParam
        {
            /// <summary>
            /// 盘点结果ID（）
            /// </summary>
            public string stockTakeResultId { get; set; }
            public string stockTakeResultCode { get; set; }
            public string stockTakeResultName { get; set; }
            public int isRedStorage { get; set; }
            public decimal money { get; set; }
            public decimal taxMoney { get; set; }
            public string storageStatus { get; set; }
            public string storageGoodsSource { get; set; }
            public string storageDate { get; set; }
            public string warehouseId { get; set; }
            public string warehouseName { get; set; }
            public string storageCode { get; set; }
            public string storageName { get; set; }
            public string contractId { get; set; }
            public string contractCode { get; set; }
            public string contractName { get; set; }
            public string supplierId { get; set; }
            public string supplier { get; set; }
            public string supplierPhone { get; set; }
            public string buyOrderId { get; set; }
            public string buyOrderCode { get; set; }
            public string buyOrderName { get; set; }
            public string buyOrderOperatorId { get; set; }
            public string buyOrderOperator { get; set; }
            public string secondParty { get; set; }
            public string secondPartyId { get; set; }
            public string receiveId { get; set; }
            public string receiveCode { get; set; }
            public string receiveName { get; set; }
            public string receiveUserId { get; set; }
            public string receiveUser { get; set; }
            public string deliveryId { get; set; }
            public string deliveryCode { get; set; }
            public string deliveryName { get; set; }
            public string checkedId { get; set; }
            public string checkedCode { get; set; }
            public string checkedName { get; set; }
            public int isPass { get; set; }
            public decimal storageMoneyTax { get; set; }
            public string initId { get; set; }
            public List<StorageInfoItem> storageInfoList { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class StorageInfoItem
        {
            public string contractCode { get; set; }
            public string orderIndex { get; set; }
            public int redStorageAllNum { get; set; }
            public string isControlledByProductDate { get; set; }
            public string deliveryWarehouseId { get; set; }
            public string deliveryWarehouse { get; set; }
            public string mgDeptName { get; set; }
            public string mgDeptId { get; set; }
            public string storageId { get; set; }
            public string checkedInfoId { get; set; }
            public string initId { get; set; }
            public string goodsId { get; set; }
            public string goodsCode { get; set; }
            public string goodsName { get; set; }
            public string goodsType { get; set; }
            public string goodsSource { get; set; }
            public string brand { get; set; }
            public string goodsVersion { get; set; }
            public string goodsAttribute { get; set; }
            public int needGoodsNum { get; set; }
            public int contractGoodsNum { get; set; }
            public int addGoodsNum { get; set; }
            public int storageNum { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public string warehouseId { get; set; }
            public string warehouseName { get; set; }
            public string shelfId { get; set; }
            public string shelfName { get; set; }
            public string batch { get; set; }
            public decimal taxPrice { get; set; }
            public decimal taxMoney { get; set; }
            public decimal tax { get; set; }
            public decimal price { get; set; }
            public decimal money { get; set; }
            public string orgId { get; set; }
            public string orgName { get; set; }
            public string deptId { get; set; }
            public string deptName { get; set; }
            public string bzId { get; set; }
            public string bzName { get; set; }
            public string gbId { get; set; }
            public string gbName { get; set; }
            public string manageDeptId { get; set; }
            public string manageDeptName { get; set; }
            public string lineId { get; set; }
            public string lineName { get; set; }
            public string gkDeptId { get; set; }
            public string gkDeptName { get; set; }
            public string localSend { get; set; }
            public string remark { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                InputParam  inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatInboundReceiptErrorMessage(message, traceId);
                }

                // 验证必填字段
                if (string.IsNullOrEmpty(inputParam.storageCode) || string.IsNullOrEmpty(inputParam.warehouseId) ||
                    string.IsNullOrEmpty(inputParam.storageDate))
                {
                    result = false;
                    message = "接口入参必填项存在空值：storageCode, warehouseId, storageDate";
                    return FormatInboundReceiptErrorMessage(message, traceId);
                }

                // 验证日期格式
                DateTime storageDate;
                if (!DateTime.TryParse(inputParam.storageDate, out storageDate))
                {
                    result = false;
                    message = $"入库日期格式错误：{inputParam.storageDate}";
                    return FormatInboundReceiptErrorMessage(message, traceId);
                }

                // 验证入库明细列表
                if (inputParam.storageInfoList == null || inputParam.storageInfoList.Count == 0)
                {
                    result = false;
                    message = "入库明细列表不能为空";
                    return FormatInboundReceiptErrorMessage(message, traceId);
                }

                // 验证入库明细项必填字段
                foreach (var item in inputParam.storageInfoList)
                {
                    if (string.IsNullOrEmpty(item.goodsCode) || item.storageNum <= 0)
                    {
                        result = false;
                        message = "入库明细项必填字段存在空值或无效值：goodsCode, storageNum";
                        return FormatInboundReceiptErrorMessage(message, traceId);
                    }
                }

                // 处理入库单主表数据
                // 这里应该根据实际的数据库模型进行数据处理
                // 由于没有具体的数据库模型定义，这里只做基本的逻辑处理

                // 检查入库单是否已存在
                var existingReceipt = S_Base.sBase.pPLAN_MAIN.GetModelPlanCode(inputParam.storageCode);

                if (existingReceipt != null) 
                {
                    result = false;
                    message = $"已存在入库单-{inputParam.storageName}";
                    return FormatInboundReceiptErrorMessage(message, traceId);
                }
                else
                {
                    // 处理入库单数据
                    // 这里应该包含：
                    // 1. 创建或更新入库单主表记录
                    // 2. 处理入库明细数据
                    // 3. 更新库存信息
                    // 4. 记录操作日志
                    PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN()
                    {
                        PLAN_CODE = inputParam.id,
                        PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanIn.ToString(),
                        PLAN_CREATE_TIME = inputParam.createDate,
                        PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString(),
                        PLAN_CREATER = inputParam.createUser,
                        PLAN_FLAG = "1",
                    };

                    IList<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();
                    foreach (StorageInfoItem storageInfoItem in inputParam.storageInfoList)
                    {
                        PLAN_LIST mPLAN_LIST = new PLAN_LIST() 
                        {
                            PLAN_LIST_CODE = storageInfoItem.id,
                            PLAN_LIST_QUANTITY = storageInfoItem.storageNum,
                            GOODS_ID = S_Base.sBase.pGOODS_MAIN.GetModel(storageInfoItem.goodsCode).GOODS_ID,

                        };
                        lsPLAN_LIST.Add(mPLAN_LIST);
                    }

                    result = S_Base.sBase.sPlan.PlanCreate(mPLAN_MAIN, lsPLAN_LIST, true, out int planID, out message);

                }
                // 模拟数据处理成功
                S_Base.sBase.Log.Info($"入库单同步成功 - 入库编码: {inputParam.storageCode}, 仓库ID: {inputParam.warehouseId}, 明细数量: {inputParam.storageInfoList.Count}");
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"入库单同步异常: {ex.Message}", ex);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = result ? 0 : 1,
                    msg = result ? "成功" : message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        /// <summary>
        /// 格式化入库单错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>格式化的错误响应</returns>
        private string FormatInboundReceiptErrorMessage(string message, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = 2, // 入参错误
                msg = message,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}