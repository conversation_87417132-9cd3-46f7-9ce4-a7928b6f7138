<?xml version="1.0"?>
<sqlMapConfig  xmlns="http://ibatis.apache.org/dataMapper" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <settings>
		<setting useStatementNamespaces="false"/>
	</settings>
  
  <database>	  
    <provider name="sqlServer2.0"/>
	  <!--生产环境-->
    <!--<dataSource name="SqlMap" connectionString="server=192.168.0.3;database=SSWMS_XZDT6;uid=sa;pwd=`123qwe"/>-->
	
	  <!--本地测试-->
	  <dataSource name="SqlMap" connectionString="server=192.168.204.219;database=SSWMS_XZDT6;uid=sa;pwd=`123qwe"/>
	  
	  <!--云服务器测试-->
	  <!--<dataSource name="SqlMap" connectionString="server=127.0.0.1;database=SSWMS_XZDT6;uid=sa;pwd=`123qwe"/>-->
  </database>

	<sqlMaps>
    <sqlMap resource="Map\MapBase.xml" />

		<sqlMap resource="Map\WMS_SQLServer\APPLY_TYPE.xml" />

		<sqlMap resource="Map\WMS_SQLServer\FLOW_ACTION.xml" />
		<sqlMap resource="Map\WMS_SQLServer\FLOW_NODE.xml" />
		<sqlMap resource="Map\WMS_SQLServer\FLOW_PARA.xml" />
		<sqlMap resource="Map\WMS_SQLServer\FLOW_TYPE.xml" />

		<sqlMap resource="Map\WMS_SQLServer\GOODS_CLASS.xml" />
		<sqlMap resource="Map\WMS_SQLServer\GOODS_MAIN.xml" />
		<sqlMap resource="Map\WMS_SQLServer\GOODS_PROPERTY.xml" />
		<sqlMap resource="Map\WMS_SQLServer\GOODS_TYPE.xml" />

		<sqlMap resource="Map\WMS_SQLServer\INTERFACE_QUEUE.xml" />

		<sqlMap resource="Map\WMS_SQLServer\IO_CONTROL.xml" />
		<sqlMap resource="Map\WMS_SQLServer\IO_CONTROL_APPLY.xml" />
		<sqlMap resource="Map\WMS_SQLServer\IO_CONTROL_APPLY_HIS.xml" />
		<sqlMap resource="Map\WMS_SQLServer\IO_CONTROL_ROUTE.xml" />

		<sqlMap resource="Map\WMS_SQLServer\LED_MAIN.xml" />
		<sqlMap resource="Map\WMS_SQLServer\LED_LIST.xml" />
		<sqlMap resource="Map\WMS_SQLServer\LCD_MAIN.xml" />
		<sqlMap resource="Map\WMS_SQLServer\LCD_LIST.xml" />

		<sqlMap resource="Map\WMS_SQLServer\MANAGE_DETAIL.xml" />
		<sqlMap resource="Map\WMS_SQLServer\MANAGE_LIST.xml" />
		<sqlMap resource="Map\WMS_SQLServer\MANAGE_MAIN.xml" />
		<sqlMap resource="Map\WMS_SQLServer\MANAGE_TYPE.xml" />

		<sqlMap resource="Map\WMS_SQLServer\PLAN_DETAIL.xml" />
		<sqlMap resource="Map\WMS_SQLServer\PLAN_LIST.xml" />
		<sqlMap resource="Map\WMS_SQLServer\PLAN_MAIN.xml" />
		<sqlMap resource="Map\WMS_SQLServer\PLAN_TYPE.xml" />

		<sqlMap resource="Map\WMS_SQLServer\RECORD_DETAIL.xml" />
		<sqlMap resource="Map\WMS_SQLServer\RECORD_LIST.xml" />
		<sqlMap resource="Map\WMS_SQLServer\RECORD_MAIN.xml" />

		<sqlMap resource="Map\WMS_SQLServer\STORAGE_DETAIL.xml" />
		<sqlMap resource="Map\WMS_SQLServer\STORAGE_LIST.xml" />
		<sqlMap resource="Map\WMS_SQLServer\STORAGE_MAIN.xml" />
		<sqlMap resource="Map\WMS_SQLServer\STORAGE_LOCK.xml" />

		<sqlMap resource="Map\WMS_SQLServer\SYS_ITEM.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_ITEM_LIST.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_LOG.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_MENU.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_RELATION.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_RELATION_LIST.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_ROLE.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_ROLE_WINDOW.xml" />
		<sqlMap resource="Map\WMS_SQLServer\SYS_USER.xml" />

		<sqlMap resource="Map\WMS_SQLServer\WH_AREA.xml" />
		<sqlMap resource="Map\WMS_SQLServer\WH_CELL.xml" />
		<sqlMap resource="Map\WMS_SQLServer\WH_DESCRIPTION.xml" />
		<sqlMap resource="Map\WMS_SQLServer\WH_LOGIC.xml" />
		<sqlMap resource="Map\WMS_SQLServer\WH_WAREHOUSE.xml" />
  </sqlMaps>
	
</sqlMapConfig>
