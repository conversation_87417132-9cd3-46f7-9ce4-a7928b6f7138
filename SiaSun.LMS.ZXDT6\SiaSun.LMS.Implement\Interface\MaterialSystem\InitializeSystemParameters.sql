-- =====================================================
-- 物资系统接口集成 - 系统参数初始化脚本
-- 创建日期：2024-08-25
-- 说明：初始化物资系统接口调用相关的系统参数
-- =====================================================

-- 检查并创建系统参数表（如果不存在）
-- 注意：根据实际数据库结构调整表结构
/*
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SYS_PARAMETER' AND xtype='U')
BEGIN
    CREATE TABLE SYS_PARAMETER (
        PARAMETER_ID INT IDENTITY(1,1) PRIMARY KEY,
        PARAMETER_KEY NVARCHAR(100) NOT NULL UNIQUE,
        PARAMETER_VALUE NVARCHAR(500),
        PARAMETER_DESCRIBE NVARCHAR(200),
        CREATE_TIME DATETIME DEFAULT GETDATE(),
        UPDATE_TIME DATETIME DEFAULT GETDATE()
    )
END
*/

-- =====================================================
-- 1. 主开关配置
-- =====================================================

-- 启用物资系统接口调用（主开关）
IF NOT EXISTS (SELECT 1 FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'EnableMaterialSystemInterface')
BEGIN
    INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_DESCRIBE) 
    VALUES ('EnableMaterialSystemInterface', 'false', '是否启用物资系统接口调用（主开关）');
    PRINT '✓ 已添加参数：EnableMaterialSystemInterface = false';
END
ELSE
BEGIN
    PRINT '○ 参数已存在：EnableMaterialSystemInterface';
END

-- =====================================================
-- 2. 分类型接口开关配置
-- =====================================================

-- 启用入库接口
IF NOT EXISTS (SELECT 1 FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'EnableInboundInterface')
BEGIN
    INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_DESCRIBE) 
    VALUES ('EnableInboundInterface', 'true', '是否启用入库完成接口调用');
    PRINT '✓ 已添加参数：EnableInboundInterface = true';
END
ELSE
BEGIN
    PRINT '○ 参数已存在：EnableInboundInterface';
END

-- 启用出库接口
IF NOT EXISTS (SELECT 1 FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'EnableOutboundInterface')
BEGIN
    INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_DESCRIBE) 
    VALUES ('EnableOutboundInterface', 'true', '是否启用出库完成接口调用');
    PRINT '✓ 已添加参数：EnableOutboundInterface = true';
END
ELSE
BEGIN
    PRINT '○ 参数已存在：EnableOutboundInterface';
END

-- 启用移库接口
IF NOT EXISTS (SELECT 1 FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'EnableTransferInterface')
BEGIN
    INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_DESCRIBE) 
    VALUES ('EnableTransferInterface', 'true', '是否启用移库结果接口调用');
    PRINT '✓ 已添加参数：EnableTransferInterface = true';
END
ELSE
BEGIN
    PRINT '○ 参数已存在：EnableTransferInterface';
END

-- =====================================================
-- 3. 性能和重试配置
-- =====================================================

-- 接口调用超时时间
IF NOT EXISTS (SELECT 1 FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'InterfaceTimeoutSeconds')
BEGIN
    INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_DESCRIBE) 
    VALUES ('InterfaceTimeoutSeconds', '30', '接口调用超时时间（秒），建议范围：10-120');
    PRINT '✓ 已添加参数：InterfaceTimeoutSeconds = 30';
END
ELSE
BEGIN
    PRINT '○ 参数已存在：InterfaceTimeoutSeconds';
END

-- 接口调用重试次数
IF NOT EXISTS (SELECT 1 FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'InterfaceRetryCount')
BEGIN
    INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_DESCRIBE) 
    VALUES ('InterfaceRetryCount', '3', '接口调用失败重试次数，建议范围：0-5');
    PRINT '✓ 已添加参数：InterfaceRetryCount = 3';
END
ELSE
BEGIN
    PRINT '○ 参数已存在：InterfaceRetryCount';
END

-- 异步接口调用
IF NOT EXISTS (SELECT 1 FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'EnableAsyncInterfaceCall')
BEGIN
    INSERT INTO SYS_PARAMETER (PARAMETER_KEY, PARAMETER_VALUE, PARAMETER_DESCRIBE) 
    VALUES ('EnableAsyncInterfaceCall', 'false', '是否启用异步接口调用（提高性能但可能影响错误处理）');
    PRINT '✓ 已添加参数：EnableAsyncInterfaceCall = false';
END
ELSE
BEGIN
    PRINT '○ 参数已存在：EnableAsyncInterfaceCall';
END

-- =====================================================
-- 4. 查看当前配置
-- =====================================================

PRINT '';
PRINT '=== 当前物资系统接口配置 ===';
SELECT 
    PARAMETER_KEY AS '参数名',
    PARAMETER_VALUE AS '参数值',
    PARAMETER_DESCRIBE AS '说明'
FROM SYS_PARAMETER 
WHERE PARAMETER_KEY IN (
    'EnableMaterialSystemInterface',
    'EnableInboundInterface', 
    'EnableOutboundInterface',
    'EnableTransferInterface',
    'InterfaceTimeoutSeconds',
    'InterfaceRetryCount',
    'EnableAsyncInterfaceCall'
)
ORDER BY 
    CASE PARAMETER_KEY
        WHEN 'EnableMaterialSystemInterface' THEN 1
        WHEN 'EnableInboundInterface' THEN 2
        WHEN 'EnableOutboundInterface' THEN 3
        WHEN 'EnableTransferInterface' THEN 4
        WHEN 'InterfaceTimeoutSeconds' THEN 5
        WHEN 'InterfaceRetryCount' THEN 6
        WHEN 'EnableAsyncInterfaceCall' THEN 7
        ELSE 99
    END;

-- =====================================================
-- 5. 启用配置的示例命令（注释状态）
-- =====================================================

/*
-- 启用物资系统接口调用
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = 'true', UPDATE_TIME = GETDATE()
WHERE PARAMETER_KEY = 'EnableMaterialSystemInterface';

-- 调整超时时间为60秒
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = '60', UPDATE_TIME = GETDATE()
WHERE PARAMETER_KEY = 'InterfaceTimeoutSeconds';

-- 调整重试次数为5次
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = '5', UPDATE_TIME = GETDATE()
WHERE PARAMETER_KEY = 'InterfaceRetryCount';

-- 启用异步调用
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = 'true', UPDATE_TIME = GETDATE()
WHERE PARAMETER_KEY = 'EnableAsyncInterfaceCall';

-- 禁用特定类型的接口
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = 'false', UPDATE_TIME = GETDATE()
WHERE PARAMETER_KEY = 'EnableTransferInterface';
*/

PRINT '';
PRINT '=== 初始化完成 ===';
PRINT '注意：';
PRINT '1. 主开关 EnableMaterialSystemInterface 默认为 false，需要手动启用';
PRINT '2. 各类型接口开关默认为 true，可根据需要调整';
PRINT '3. 建议在测试环境验证后再在生产环境启用';
PRINT '4. 可通过日志监控接口调用情况';
