using System;
using System.Collections.Generic;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement.Interface.MaterialSystem
{
    /// <summary>
    /// 物资系统接口调用策略接口
    /// </summary>
    public interface IMaterialSystemInterfaceStrategy
    {
        /// <summary>
        /// 调用物资系统接口
        /// </summary>
        /// <param name="manageMain">任务主表信息</param>
        /// <param name="manageList">任务明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        bool CallInterface(MANAGE_MAIN manageMain, IList<MANAGE_LIST> manageList, out string message);

        /// <summary>
        /// 获取策略名称
        /// </summary>
        string StrategyName { get; }
    }
}
