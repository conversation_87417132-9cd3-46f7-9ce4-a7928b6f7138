using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Xml;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 移库结果上报回调接口 【iWMS提供，SSWMS调用】
    /// 注册(regist) -> 获取凭证(getCertificate) -> 携带凭证调用 accessInterface(移库结果上报)
    /// </summary>
    public class TransferResultCallback : InterfaceBase
    {
        /// <summary>
        /// 访问接口的Response结构
        /// </summary>
        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string data { get; set; }
        }

        /// <summary>
        /// 移库结果明细项
        /// </summary>
        public class TransferResultItem
        {
            /// <summary>
            /// 目标货架位编号
            /// </summary>
            public string targetShelfCode { get; set; }

            /// <summary>
            /// 目标货架位名称
            /// </summary>
            public string targetShelfName { get; set; }

            /// <summary>
            /// 目标货位id
            /// </summary>
            public string targetShelfId { get; set; }

            /// <summary>
            /// 目标库房编号
            /// </summary>
            public string targetWarehouseCode { get; set; }

            /// <summary>
            /// 目标库房名称
            /// </summary>
            public string targetWarehouseName { get; set; }

            /// <summary>
            /// 目标库房id
            /// </summary>
            public string targetWarehouseId { get; set; }

            /// <summary>
            /// 移库数量
            /// </summary>
            public int removeNum { get; set; }

            /// <summary>
            /// 原货架位名称
            /// </summary>
            public string shelfName { get; set; }

            /// <summary>
            /// 原货架位编号
            /// </summary>
            public string shelfCode { get; set; }

            /// <summary>
            /// 原货位id
            /// </summary>
            public string shelfId { get; set; }

            /// <summary>
            /// 原库房编号
            /// </summary>
            public string warehouseCode { get; set; }

            /// <summary>
            /// 原库房名称
            /// </summary>
            public string warehouseName { get; set; }

            /// <summary>
            /// 原库房id
            /// </summary>
            public string warehouseId { get; set; }

            /// <summary>
            /// 物资编码
            /// </summary>
            public string goodsCode { get; set; }
        }

        /// <summary>
        /// 调用外部物资系统移库结果上报回调（批量明细）
        /// </summary>
        /// <param name="transferResultItems">移库结果明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(List<TransferResultItem> transferResultItems, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 基础校验
                if (transferResultItems == null || transferResultItems.Count == 0)
                {
                    result = false;
                    message = "参数错误：transferResultItems 不能为空";
                    return result;
                }

                // 校验每个明细项的必填字段
                for (int i = 0; i < transferResultItems.Count; i++)
                {
                    var item = transferResultItems[i];
                    if (string.IsNullOrEmpty(item.goodsCode))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的goodsCode不能为空";
                        return result;
                    }
                    if (string.IsNullOrEmpty(item.warehouseCode))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的warehouseCode不能为空";
                        return result;
                    }
                    if (string.IsNullOrEmpty(item.shelfCode))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的shelfCode不能为空";
                        return result;
                    }
                    if (string.IsNullOrEmpty(item.targetWarehouseCode))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的targetWarehouseCode不能为空";
                        return result;
                    }
                    if (string.IsNullOrEmpty(item.targetShelfCode))
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的targetShelfCode不能为空";
                        return result;
                    }
                    if (item.removeNum <= 0)
                    {
                        result = false;
                        message = $"参数错误：第{i + 1}项的removeNum必须大于0";
                        return result;
                    }
                }

                // Step 1: 注册，获取secrit
                string registInput = Common.JsonHelper.Serializer(new { systemIdentify = "ZYK" });
                if (!InvokeExternal("regist", registInput, out string registOut))
                {
                    result = false;
                    message = $"调用regist失败：{registOut}";
                    return result;
                }

                var registResp = TryParse<OutputParam>(registOut, out string parseErr1);
                if (registResp == null || registResp.code != 200 || string.IsNullOrEmpty(registResp.data))
                {
                    result = false;
                    message = $"regist返回无效：{(registResp == null ? parseErr1 : registResp.msg)}";
                    return result;
                }

                string secrit = registResp.data;

                // Step 2: 获取凭证 certificate
                var applyTokenInputObj = new
                {
                    interfaceIdentify = "warehouseRemove",
                    publicKey = secrit,
                    systemIdentify = "ZYK",
                    tokenExpired = "2"
                };
                string applyTokenInput = Common.JsonHelper.Serializer(applyTokenInputObj);
                if (!InvokeExternal("getCertificate", applyTokenInput, out string applyTokenOut))
                {
                    result = false;
                    message = $"调用getCertificate失败：{applyTokenOut}";
                    return result;
                }

                var applyResp = TryParse<OutputParam>(applyTokenOut, out string parseErr2);
                if (applyResp == null || applyResp.code != 200 || string.IsNullOrEmpty(applyResp.data))
                {
                    result = false;
                    message = $"getCertificate返回无效：{(applyResp == null ? parseErr2 : applyResp.msg)}";
                    return result;
                }

                string certificate = applyResp.data;

                // Step 3: 组织业务负载并调用 accessInterface(移库结果上报)
                string payloadJson = Common.JsonHelper.Serializer(transferResultItems);

                // 按Java参考实现，accessInterface 的 paramsJSONStr 结构
                string paramsJSONStr = Common.JsonHelper.Serializer(new
                {
                    systemIdentify = "ZYK",
                    interfaceIdentify = "warehouseRemove",
                    certificate = certificate,
                    json = payloadJson
                });

                string accessInterfaceInput = paramsJSONStr;
                if (!InvokeExternal("accessInterface", accessInterfaceInput, out string accessInterfaceOut))
                {
                    result = false;
                    message = $"调用accessInterface失败：{accessInterfaceOut}";
                    return result;
                }

                var accessResp = TryParse<OutputParam>(accessInterfaceOut, out string parseErr3);
                if (accessResp == null || accessResp.code != 200)
                {
                    result = false;
                    message = $"accessInterface返回无效：{(accessResp == null ? parseErr3 : accessResp.msg)}";
                    return result;
                }

                message = "移库结果上报成功";
                S_Base.sBase.Log.Info($"TransferResultCallback成功_明细数[{transferResultItems.Count}]_traceId[{Guid.NewGuid()}]_信息[{message}]");
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
                S_Base.sBase.Log.Error($"TransferResultCallback异常：{ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 调用外部物资系统移库结果上报（单个明细）
        /// </summary>
        /// <param name="goodsCode">物资编码</param>
        /// <param name="removeNum">移库数量</param>
        /// <param name="warehouseCode">原库房编号</param>
        /// <param name="warehouseName">原库房名称</param>
        /// <param name="warehouseId">原库房id</param>
        /// <param name="shelfCode">原货架位编号</param>
        /// <param name="shelfName">原货架位名称</param>
        /// <param name="shelfId">原货位id</param>
        /// <param name="targetWarehouseCode">目标库房编号</param>
        /// <param name="targetWarehouseName">目标库房名称</param>
        /// <param name="targetWarehouseId">目标库房id</param>
        /// <param name="targetShelfCode">目标货架位编号</param>
        /// <param name="targetShelfName">目标货架位名称</param>
        /// <param name="targetShelfId">目标货位id</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(string goodsCode, int removeNum,
            string warehouseCode, string warehouseName, string warehouseId,
            string shelfCode, string shelfName, string shelfId,
            string targetWarehouseCode, string targetWarehouseName, string targetWarehouseId,
            string targetShelfCode, string targetShelfName, string targetShelfId,
            out string message)
        {
            var items = new List<TransferResultItem>
            {
                new TransferResultItem
                {
                    goodsCode = goodsCode,
                    removeNum = removeNum,
                    warehouseCode = warehouseCode,
                    warehouseName = warehouseName,
                    warehouseId = warehouseId,
                    shelfCode = shelfCode,
                    shelfName = shelfName,
                    shelfId = shelfId,
                    targetWarehouseCode = targetWarehouseCode,
                    targetWarehouseName = targetWarehouseName,
                    targetWarehouseId = targetWarehouseId,
                    targetShelfCode = targetShelfCode,
                    targetShelfName = targetShelfName,
                    targetShelfId = targetShelfId
                }
            };

            return IntefaceMethod(items, out message);
        }

        private T TryParse<T>(string jsonOrWrapped, out string error) where T : class
        {
            error = null;
            try
            {
                // 某些WebService返回SOAP中包含<String>{json}</String>，
                // 若传入已是纯json字符串也可直接解析
                string candidate = jsonOrWrapped;
                // 提取可能包裹的XML中的<String>内容
                if (!string.IsNullOrEmpty(candidate) && candidate.Contains("<"))
                {
                    try
                    {
                        var xml = new XmlDocument();
                        xml.LoadXml(candidate);
                        var node = xml.SelectSingleNode("//String");
                        if (node != null)
                        {
                            candidate = node.InnerText;
                        }
                    }
                    catch { /* ignore xml parse */ }
                }
                return Common.JsonHelper.Deserialize<T>(candidate);
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return null;
            }
        }


    }
}
