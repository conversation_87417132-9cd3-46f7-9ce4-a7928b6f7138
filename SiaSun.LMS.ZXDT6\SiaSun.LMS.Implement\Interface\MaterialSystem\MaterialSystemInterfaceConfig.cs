using System;
using System.Collections.Generic;

namespace SiaSun.LMS.Implement.Interface.MaterialSystem
{
    /// <summary>
    /// 物资系统接口配置管理类
    /// </summary>
    public static class MaterialSystemInterfaceConfig
    {
        /// <summary>
        /// 配置键名常量
        /// </summary>
        public static class ConfigKeys
        {
            /// <summary>
            /// 是否启用物资系统接口调用
            /// </summary>
            public const string ENABLE_MATERIAL_SYSTEM_INTERFACE = "EnableMaterialSystemInterface";

            /// <summary>
            /// 入库接口是否启用
            /// </summary>
            public const string ENABLE_INBOUND_INTERFACE = "EnableInboundInterface";

            /// <summary>
            /// 出库接口是否启用
            /// </summary>
            public const string ENABLE_OUTBOUND_INTERFACE = "EnableOutboundInterface";

            /// <summary>
            /// 移库接口是否启用
            /// </summary>
            public const string ENABLE_TRANSFER_INTERFACE = "EnableTransferInterface";

            /// <summary>
            /// 接口调用超时时间（秒）
            /// </summary>
            public const string INTERFACE_TIMEOUT_SECONDS = "InterfaceTimeoutSeconds";

            /// <summary>
            /// 接口调用失败重试次数
            /// </summary>
            public const string INTERFACE_RETRY_COUNT = "InterfaceRetryCount";

            /// <summary>
            /// 是否异步调用接口
            /// </summary>
            public const string ENABLE_ASYNC_INTERFACE_CALL = "EnableAsyncInterfaceCall";
        }

        /// <summary>
        /// 默认配置值
        /// </summary>
        public static class DefaultValues
        {
            public const bool ENABLE_MATERIAL_SYSTEM_INTERFACE = false;
            public const bool ENABLE_INBOUND_INTERFACE = true;
            public const bool ENABLE_OUTBOUND_INTERFACE = true;
            public const bool ENABLE_TRANSFER_INTERFACE = true;
            public const int INTERFACE_TIMEOUT_SECONDS = 30;
            public const int INTERFACE_RETRY_COUNT = 3;
            public const bool ENABLE_ASYNC_INTERFACE_CALL = false;
        }

        /// <summary>
        /// 检查是否启用物资系统接口调用
        /// </summary>
        /// <returns>是否启用</returns>
        public static bool IsEnabled()
        {
            string value = S_Base.sBase.sSystem.GetSysParameter(
                ConfigKeys.ENABLE_MATERIAL_SYSTEM_INTERFACE, 
                DefaultValues.ENABLE_MATERIAL_SYSTEM_INTERFACE.ToString());
            
            return bool.TryParse(value, out bool result) && result;
        }

        /// <summary>
        /// 检查指定任务类型的接口是否启用
        /// </summary>
        /// <param name="manageTypeInOut">任务出入库类型：1=入库，2=出库，3=移库</param>
        /// <returns>是否启用</returns>
        public static bool IsInterfaceEnabled(string manageTypeInOut)
        {
            if (!IsEnabled())
            {
                return false;
            }

            string configKey;
            bool defaultValue;

            switch (manageTypeInOut)
            {
                case "1": // 入库
                    configKey = ConfigKeys.ENABLE_INBOUND_INTERFACE;
                    defaultValue = DefaultValues.ENABLE_INBOUND_INTERFACE;
                    break;

                case "2": // 出库
                    configKey = ConfigKeys.ENABLE_OUTBOUND_INTERFACE;
                    defaultValue = DefaultValues.ENABLE_OUTBOUND_INTERFACE;
                    break;

                case "3": // 移库
                    configKey = ConfigKeys.ENABLE_TRANSFER_INTERFACE;
                    defaultValue = DefaultValues.ENABLE_TRANSFER_INTERFACE;
                    break;

                default:
                    return false;
            }

            string value = S_Base.sBase.sSystem.GetSysParameter(configKey, defaultValue.ToString());
            return bool.TryParse(value, out bool result) && result;
        }

        /// <summary>
        /// 获取接口调用超时时间（秒）
        /// </summary>
        /// <returns>超时时间</returns>
        public static int GetTimeoutSeconds()
        {
            string value = S_Base.sBase.sSystem.GetSysParameter(
                ConfigKeys.INTERFACE_TIMEOUT_SECONDS, 
                DefaultValues.INTERFACE_TIMEOUT_SECONDS.ToString());
            
            return int.TryParse(value, out int result) ? result : DefaultValues.INTERFACE_TIMEOUT_SECONDS;
        }

        /// <summary>
        /// 获取接口调用失败重试次数
        /// </summary>
        /// <returns>重试次数</returns>
        public static int GetRetryCount()
        {
            string value = S_Base.sBase.sSystem.GetSysParameter(
                ConfigKeys.INTERFACE_RETRY_COUNT, 
                DefaultValues.INTERFACE_RETRY_COUNT.ToString());
            
            return int.TryParse(value, out int result) ? result : DefaultValues.INTERFACE_RETRY_COUNT;
        }

        /// <summary>
        /// 检查是否启用异步接口调用
        /// </summary>
        /// <returns>是否启用异步调用</returns>
        public static bool IsAsyncCallEnabled()
        {
            string value = S_Base.sBase.sSystem.GetSysParameter(
                ConfigKeys.ENABLE_ASYNC_INTERFACE_CALL, 
                DefaultValues.ENABLE_ASYNC_INTERFACE_CALL.ToString());
            
            return bool.TryParse(value, out bool result) && result;
        }

        /// <summary>
        /// 获取所有配置信息（用于调试和监控）
        /// </summary>
        /// <returns>配置信息字典</returns>
        public static Dictionary<string, object> GetAllConfigs()
        {
            return new Dictionary<string, object>
            {
                { ConfigKeys.ENABLE_MATERIAL_SYSTEM_INTERFACE, IsEnabled() },
                { ConfigKeys.ENABLE_INBOUND_INTERFACE, IsInterfaceEnabled("1") },
                { ConfigKeys.ENABLE_OUTBOUND_INTERFACE, IsInterfaceEnabled("2") },
                { ConfigKeys.ENABLE_TRANSFER_INTERFACE, IsInterfaceEnabled("3") },
                { ConfigKeys.INTERFACE_TIMEOUT_SECONDS, GetTimeoutSeconds() },
                { ConfigKeys.INTERFACE_RETRY_COUNT, GetRetryCount() },
                { ConfigKeys.ENABLE_ASYNC_INTERFACE_CALL, IsAsyncCallEnabled() }
            };
        }

        /// <summary>
        /// 打印当前配置信息到日志
        /// </summary>
        public static void LogCurrentConfigs()
        {
            var configs = GetAllConfigs();
            S_Base.sBase.Log.Info("=== 物资系统接口配置信息 ===");
            
            foreach (var config in configs)
            {
                S_Base.sBase.Log.Info($"{config.Key} = {config.Value}");
            }
            
            S_Base.sBase.Log.Info("=== 配置信息结束 ===");
        }

        /// <summary>
        /// 验证配置的有效性
        /// </summary>
        /// <returns>配置验证结果</returns>
        public static (bool IsValid, List<string> Errors) ValidateConfigs()
        {
            var errors = new List<string>();

            // 验证超时时间
            int timeout = GetTimeoutSeconds();
            if (timeout <= 0 || timeout > 300)
            {
                errors.Add($"接口超时时间配置无效：{timeout}秒，应在1-300秒之间");
            }

            // 验证重试次数
            int retryCount = GetRetryCount();
            if (retryCount < 0 || retryCount > 10)
            {
                errors.Add($"接口重试次数配置无效：{retryCount}次，应在0-10次之间");
            }

            return (errors.Count == 0, errors);
        }
    }
}
