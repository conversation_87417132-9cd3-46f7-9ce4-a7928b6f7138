# 物资系统接口集成 - 部署和测试指南

## 部署步骤

### 1. 代码部署

#### 1.1 文件清单
确保以下文件已正确部署到项目中：

```
SiaSun.LMS.Implement\Interface\MaterialSystem\
├── IMaterialSystemInterfaceStrategy.cs          # 接口策略基类
├── MaterialSystemInterfaceFactory.cs            # 策略工厂类
├── MaterialSystemInterfaceConfig.cs             # 配置管理类
├── InboundInterfaceStrategy.cs                  # 入库接口策略
├── OutboundInterfaceStrategy.cs                 # 出库接口策略
├── TransferInterfaceStrategy.cs                 # 移库接口策略
├── MaterialSystemInterfaceTest.cs               # 测试类
├── InitializeSystemParameters.sql               # 参数初始化脚本
├── README.md                                    # 使用说明
└── DeploymentGuide.md                           # 部署指南（本文件）

SiaSun.LMS.Implement\S_Manage.cs                 # 已修改的主要业务文件
```

#### 1.2 代码修改确认
检查 `S_Manage.cs` 文件中的以下修改：

1. **引用添加**（第11行）：
   ```csharp
   using SiaSun.LMS.Implement.Interface.MaterialSystem;
   ```

2. **接口调用代码**（第767-774行）：
   ```csharp
   //调用物资系统接口
   bResult = CallMaterialSystemInterface(mMANAGE_MAIN, lsMANAGE_LIST, mMANAGE_TYPE, out string interfaceMessage);
   if (!bResult)
   {
       // 接口调用失败不影响任务完成，只记录日志
       S_Base.sBase.Log.Warn($"物资系统接口调用失败_任务ID[{mMANAGE_MAIN.MANAGE_ID}]_任务类型[{mMANAGE_MAIN.MANAGE_TYPE_CODE}]_信息[{interfaceMessage}]");
       // 重置结果为成功，继续完成任务
       bResult = true;
   }
   ```

3. **新增方法**（第799-877行）：
   ```csharp
   private bool CallMaterialSystemInterface(...)
   ```

### 2. 数据库配置

#### 2.1 执行初始化脚本
运行 `InitializeSystemParameters.sql` 脚本：

```sql
-- 在数据库中执行
sqlcmd -S [服务器] -d [数据库] -i InitializeSystemParameters.sql
```

或者在SQL Server Management Studio中直接执行脚本内容。

#### 2.2 验证参数创建
执行以下查询验证参数是否正确创建：

```sql
SELECT 
    PARAMETER_KEY AS '参数名',
    PARAMETER_VALUE AS '参数值',
    PARAMETER_REMARK AS '说明'
FROM SYS_PARAMETER 
WHERE PARAMETER_KEY LIKE '%Interface%'
ORDER BY PARAMETER_KEY;
```

### 3. 编译和发布

#### 3.1 编译项目
```bash
# 在项目根目录执行
msbuild SiaSun.LMS.ZXDT6.sln /p:Configuration=Release
```

#### 3.2 检查编译结果
确保没有编译错误，只有代码风格警告是可以接受的。

#### 3.3 发布到目标环境
将编译后的文件部署到目标服务器。

## 测试步骤

### 1. 单元测试

#### 1.1 配置测试
```csharp
// 在测试环境中执行
MaterialSystemInterfaceConfig.LogCurrentConfigs();
var (isValid, errors) = MaterialSystemInterfaceConfig.ValidateConfigs();
```

#### 1.2 工厂模式测试
```csharp
MaterialSystemInterfaceTest.TestFactory();
```

#### 1.3 接口策略测试
```csharp
// 测试各种接口策略
MaterialSystemInterfaceTest.TestInboundInterface();
MaterialSystemInterfaceTest.TestOutboundInterface();
MaterialSystemInterfaceTest.TestTransferInterface();
```

### 2. 集成测试

#### 2.1 启用测试模式
```sql
-- 启用物资系统接口调用
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = 'true' 
WHERE PARAMETER_KEY = 'EnableMaterialSystemInterface';

-- 设置较短的超时时间用于测试
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = '10' 
WHERE PARAMETER_KEY = 'InterfaceTimeoutSeconds';

-- 设置重试次数为1次用于测试
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = '1' 
WHERE PARAMETER_KEY = 'InterfaceRetryCount';
```

#### 2.2 创建测试任务
创建入库、出库、移库测试任务，观察任务完成时的接口调用情况。

#### 2.3 监控日志
观察以下关键日志：

**成功日志示例：**
```
[INFO] 物资系统接口调用成功_任务ID[1001]_任务类型[ManageIn]_信息[入库单完成上报成功]
[INFO] 入库完成接口调用成功_任务ID[1001]_托盘条码[TEST_001]_明细数量[2]
```

**失败日志示例：**
```
[WARN] 物资系统接口调用失败_任务ID[1002]_任务类型[ManageOut]_信息[调用regist失败：网络超时]
[ERROR] 物资系统接口调用失败，已达最大重试次数_任务ID[1002]_任务类型[ManageOut]_信息[...]
```

### 3. 性能测试

#### 3.1 测试接口调用对任务完成时间的影响
```sql
-- 记录任务完成时间
SELECT 
    MANAGE_ID,
    MANAGE_BEGIN_TIME,
    MANAGE_END_TIME,
    DATEDIFF(second, MANAGE_BEGIN_TIME, MANAGE_END_TIME) as Duration
FROM MANAGE_MAIN 
WHERE MANAGE_END_TIME IS NOT NULL
ORDER BY MANAGE_END_TIME DESC;
```

#### 3.2 测试并发场景
同时完成多个任务，观察接口调用的并发处理能力。

## 生产环境部署

### 1. 预生产验证

#### 1.1 功能验证清单
- [ ] 所有文件正确部署
- [ ] 数据库参数正确配置
- [ ] 编译无错误
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试满足要求
- [ ] 日志记录正常
- [ ] 错误处理正确

#### 1.2 回滚方案准备
```sql
-- 紧急禁用接口调用
UPDATE SYS_PARAMETER 
SET PARAMETER_VALUE = 'false' 
WHERE PARAMETER_KEY = 'EnableMaterialSystemInterface';
```

### 2. 生产环境配置

#### 2.1 推荐的生产环境参数
```sql
-- 主开关：根据实际需要设置
UPDATE SYS_PARAMETER SET PARAMETER_VALUE = 'true' WHERE PARAMETER_KEY = 'EnableMaterialSystemInterface';

-- 超时时间：建议30-60秒
UPDATE SYS_PARAMETER SET PARAMETER_VALUE = '60' WHERE PARAMETER_KEY = 'InterfaceTimeoutSeconds';

-- 重试次数：建议3-5次
UPDATE SYS_PARAMETER SET PARAMETER_VALUE = '3' WHERE PARAMETER_KEY = 'InterfaceRetryCount';

-- 异步调用：根据性能需求决定
UPDATE SYS_PARAMETER SET PARAMETER_VALUE = 'false' WHERE PARAMETER_KEY = 'EnableAsyncInterfaceCall';
```

#### 2.2 监控配置
设置以下监控项：
- 接口调用成功率
- 接口调用平均响应时间
- 接口调用失败告警
- 任务完成时间变化

### 3. 运维指南

#### 3.1 常见问题处理

**问题1：接口调用失败率高**
```sql
-- 临时禁用有问题的接口类型
UPDATE SYS_PARAMETER SET PARAMETER_VALUE = 'false' WHERE PARAMETER_KEY = 'EnableInboundInterface';
```

**问题2：接口调用超时**
```sql
-- 增加超时时间
UPDATE SYS_PARAMETER SET PARAMETER_VALUE = '120' WHERE PARAMETER_KEY = 'InterfaceTimeoutSeconds';
```

**问题3：任务完成变慢**
```sql
-- 启用异步调用
UPDATE SYS_PARAMETER SET PARAMETER_VALUE = 'true' WHERE PARAMETER_KEY = 'EnableAsyncInterfaceCall';
```

#### 3.2 日常维护

**每日检查：**
- 查看接口调用失败日志
- 检查任务完成时间是否正常
- 监控系统性能指标

**每周检查：**
- 分析接口调用统计数据
- 检查配置参数是否需要调整
- 清理过期日志文件

**每月检查：**
- 评估接口调用性能趋势
- 检查是否需要优化配置
- 更新监控告警阈值

## 故障排查

### 1. 诊断命令

```csharp
// 检查配置
MaterialSystemInterfaceConfig.LogCurrentConfigs();

// 验证配置
var (isValid, errors) = MaterialSystemInterfaceConfig.ValidateConfigs();

// 测试策略创建
var strategy = MaterialSystemInterfaceFactory.CreateStrategy("1");

// 运行完整测试
MaterialSystemInterfaceTest.RunAllTests();
```

### 2. 日志分析

查看关键日志文件，重点关注：
- 接口调用成功/失败日志
- 重试日志
- 异常日志
- 配置变更日志

### 3. 性能分析

监控以下指标：
- 任务完成平均时间
- 接口调用平均响应时间
- 接口调用成功率
- 系统资源使用情况

## 联系支持

如遇到问题，请提供以下信息：
1. 错误日志详细信息
2. 当前系统参数配置
3. 问题复现步骤
4. 系统环境信息

---

**注意事项：**
1. 在生产环境部署前务必在测试环境充分验证
2. 建议分阶段启用功能，先启用一种任务类型测试
3. 密切监控系统性能，及时调整配置参数
4. 保持日志记录完整，便于问题排查
