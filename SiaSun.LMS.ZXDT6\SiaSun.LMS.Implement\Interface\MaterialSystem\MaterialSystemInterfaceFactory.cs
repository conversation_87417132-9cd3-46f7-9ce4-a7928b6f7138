using System;
using System.Collections.Generic;

namespace SiaSun.LMS.Implement.Interface.MaterialSystem
{
    /// <summary>
    /// 物资系统接口策略工厂类
    /// </summary>
    public static class MaterialSystemInterfaceFactory
    {
        /// <summary>
        /// 策略缓存，避免重复创建实例
        /// </summary>
        private static readonly Dictionary<string, IMaterialSystemInterfaceStrategy> _strategyCache 
            = new Dictionary<string, IMaterialSystemInterfaceStrategy>();

        /// <summary>
        /// 根据任务类型创建相应的接口调用策略
        /// </summary>
        /// <param name="manageTypeInOut">任务出入库类型：1=入库，2=出库，3=移库</param>
        /// <returns>接口调用策略实例，如果不支持则返回null</returns>
        public static IMaterialSystemInterfaceStrategy CreateStrategy(string manageTypeInOut)
        {
            if (string.IsNullOrEmpty(manageTypeInOut))
            {
                return null;
            }

            // 从缓存中获取策略实例
            if (_strategyCache.TryGetValue(manageTypeInOut, out IMaterialSystemInterfaceStrategy cachedStrategy))
            {
                return cachedStrategy;
            }

            IMaterialSystemInterfaceStrategy strategy = null;

            try
            {
                switch (manageTypeInOut)
                {
                    case "1": // 入库
                        strategy = new InboundInterfaceStrategy();
                        break;

                    case "2": // 出库
                        strategy = new OutboundInterfaceStrategy();
                        break;

                    case "3": // 移库
                        strategy = new TransferInterfaceStrategy();
                        break;

                    default:
                        // 不支持的任务类型
                        S_Base.sBase.Log.Debug($"不支持的任务类型：{manageTypeInOut}");
                        return null;
                }

                // 将创建的策略实例加入缓存
                if (strategy != null)
                {
                    _strategyCache[manageTypeInOut] = strategy;
                    S_Base.sBase.Log.Debug($"创建物资系统接口策略：{strategy.StrategyName}");
                }
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"创建物资系统接口策略失败_任务类型[{manageTypeInOut}]_异常信息[{ex.Message}]", ex);
                return null;
            }

            return strategy;
        }

        /// <summary>
        /// 清空策略缓存（主要用于测试或重新加载配置）
        /// </summary>
        public static void ClearCache()
        {
            _strategyCache.Clear();
            S_Base.sBase.Log.Debug("物资系统接口策略缓存已清空");
        }

        /// <summary>
        /// 获取所有支持的任务类型
        /// </summary>
        /// <returns>支持的任务类型列表</returns>
        public static string[] GetSupportedTaskTypes()
        {
            return new string[] { "1", "2", "3" }; // 入库、出库、移库
        }
    }
}
