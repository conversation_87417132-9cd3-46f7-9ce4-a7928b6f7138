using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 4.16 同步物料信息接口 【SSWMS提供，iWMS调用】
    /// 支持批量处理物料信息数组
    /// </summary>
    public class GoodsInfoSync : InterfaceBase
    {
        /// <summary>
        /// 批量输入参数包装类
        /// </summary>
        class BatchInputParam
        {
            /// <summary>
            /// 物料信息数组
            /// </summary>
            public List<InputParam> goodsList { get; set; }
        }

        /// <summary>
        /// 单个物料信息参数类
        /// </summary>
        class InputParam
        {
            public string daLeiId { get; set; }
            public string daLeiName { get; set; }
            public string xiaoLeiId { get; set; }
            public string xiaoLeiName { get; set; }
            public string daZuId { get; set; }
            public string daZuName { get; set; }
            public string xiaoZuId { get; set; }
            public string xiaoZuName { get; set; }
            public int goodsStatus { get; set; }
            public string code { get; set; }
            public string parentId { get; set; }
            public string parentName { get; set; }
            /// <summary>
            /// 物资名称（GOODS_NAME）
            /// </summary>
            public string name { get; set; }
            public string goodsType { get; set; }
            public string goodsAttribute { get; set; }
            /// <summary>
            /// 计量单位ID（GOODS_CONST_PROPERTY3）
            /// </summary>
            public string unitId { get; set; }
            /// <summary>
            /// 计量单位名称（GOODS_UNIT）
            /// </summary>
            public string unitName { get; set; }
            /// <summary>
            /// 规格型号（GOODS_CONST_PROPERTY2）
            /// </summary>
            public string goodsVersion { get; set; }
            public string goodsClass { get; set; }
            /// <summary>
            /// 物资编码（GOODS_CODE）
            /// </summary>
            public string goodsCode { get; set; }
            public decimal suggestedAmount { get; set; }
            public int isLaborInsuranceMaterials { get; set; }
            public bool ignoreParent { get; set; }
            public string fixedAssetFlag { get; set; }
            public string professionalAssetFlag { get; set; }
            public UnitInformationEntityDTO unitInformationEntityDTO { get; set; }
            public List<BrandVO> brandVOs { get; set; }
            public string isControlledByProductDate { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class UnitInformationEntityDTO
        {
            /// <summary>
            /// 物资ID（GOODS_CONST_PROPERTY1)
            /// </summary>
            public string goodsId { get; set; }
            public List<ConversionDetailVO> conversionDetailVOs { get; set; }
            public int grossWeight { get; set; }
            public int netWeight { get; set; }
            public int unitHeight { get; set; }
            public int unitWide { get; set; }
            public int unitLength { get; set; }
            public string unit { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class ConversionDetailVO
        {
            public string conversionRatio { get; set; }
            public string conversionUnit { get; set; }
        }

        class BrandVO
        {
            /// <summary>
            /// 品牌（GOODS_CONST_PROPERTY4）
            /// </summary>
            public string brandName { get; set; }
            public string purchasesNum { get; set; }
            public string initialPutawayTime { get; set; }
            public int inboundQuantity { get; set; }
            public string remark { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            int resultCode = 0;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                // 尝试解析为批量输入参数
                BatchInputParam batchInputParam = null;
                List<InputParam> inputParams = new List<InputParam>();

                // 首先尝试解析为批量格式
                try
                {
                    batchInputParam = Common.JsonHelper.Deserialize<BatchInputParam>(inputJson);
                    if (batchInputParam != null && batchInputParam.goodsList != null && batchInputParam.goodsList.Count > 0)
                    {
                        inputParams = batchInputParam.goodsList;
                        S_Base.sBase.Log.Info($"GoodsInfoSync接收到批量数据_数量[{inputParams.Count}]_traceId[{traceId}]");
                    }
                }
                catch (Exception batchEx)
                {
                    // 批量解析失败，记录日志并尝试解析为单个对象
                    S_Base.sBase.Log.Info($"GoodsInfoSync批量格式解析失败_异常[{batchEx.Message}]_尝试单个格式解析_traceId[{traceId}]");
                }

                // 如果批量解析失败，尝试解析为单个对象（向后兼容）
                if (inputParams.Count == 0)
                {
                    try
                    {
                        InputParam singleInputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                        if (singleInputParam != null)
                        {
                            inputParams.Add(singleInputParam);
                            S_Base.sBase.Log.Info($"GoodsInfoSync接收到单个数据_traceId[{traceId}]");
                        }
                    }
                    catch (Exception singleEx)
                    {
                        // 单个对象解析也失败，记录详细错误信息
                        S_Base.sBase.Log.Error($"GoodsInfoSync单个格式解析失败_异常[{singleEx.Message}]_入参长度[{inputJson?.Length ?? 0}]_traceId[{traceId}]");
                    }
                }

                // 如果两种解析都失败
                if (inputParams.Count == 0)
                {
                    resultCode = 2;
                    message = $"调用入参解析错误，无法解析为有效的物料信息格式";
                    return FormatResponse(resultCode, message, traceId);
                }

                // 批量处理每个物料信息
                int successCount = 0;
                int failCount = 0;
                List<string> errorMessages = new List<string>();

                foreach (var inputParam in inputParams)
                {
                    try
                    {
                        string singleResult = ProcessSingleGoods(inputParam, traceId);
                        if (string.IsNullOrEmpty(singleResult))
                        {
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                            errorMessages.Add($"物料[{inputParam?.goodsCode ?? "未知"}]:{singleResult}");
                        }
                    }
                    catch (Exception processEx)
                    {
                        failCount++;
                        string errorMsg = $"物料[{inputParam?.goodsCode ?? "未知"}]处理异常:{processEx.Message}";
                        errorMessages.Add(errorMsg);
                        S_Base.sBase.Log.Error($"GoodsInfoSync单个物料处理异常_物料编码[{inputParam?.goodsCode ?? "未知"}]_异常[{processEx.Message}]_traceId[{traceId}]");
                    }
                }

                // 设置处理结果汇总信息
                if (failCount == 0)
                {
                    resultCode = 0;
                    message = $"批量处理完成_总数[{inputParams.Count}]_成功[{successCount}]_失败[{failCount}]";
                }
                else if (successCount > 0)
                {
                    resultCode = 1; // 部分成功
                    message = $"批量处理部分成功_总数[{inputParams.Count}]_成功[{successCount}]_失败[{failCount}]";
                    if (errorMessages.Count > 0)
                    {
                        message += $"_错误详情[{string.Join(";", errorMessages)}]";
                    }
                }
                else
                {
                    resultCode = 2; // 全部失败
                    message = $"批量处理失败_总数[{inputParams.Count}]_成功[{successCount}]_失败[{failCount}]";
                    if (errorMessages.Count > 0)
                    {
                        message += $"_错误详情[{string.Join(";", errorMessages)}]";
                    }
                }

                // Log the operation
                S_Base.sBase.Log.Info($"GoodsInfoSync批量处理完成_入参长度[{inputJson?.Length ?? 0}]_结果[{message}]_traceId[{traceId}]");
            }
            catch (Exception ex)
            {
                resultCode = 1;
                message = string.Format("批量处理异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"GoodsInfoSync批量处理异常_入参长度[{inputJson?.Length ?? 0}]_异常[{ex.Message}]_堆栈[{ex.StackTrace}]_traceId[{traceId}]");
            }
            finally
            {
                try
                {
                    outputParam = new OutputParam()
                    {
                        code = resultCode,
                        msg = message,
                        traceId = traceId
                    };
                    outputJson = Common.JsonHelper.Serializer(outputParam);
                }
                catch (Exception serializeEx)
                {
                    // 序列化异常处理
                    S_Base.sBase.Log.Error($"GoodsInfoSync输出序列化异常_异常[{serializeEx.Message}]_traceId[{traceId}]");
                    outputJson = $"{{\"code\":1,\"msg\":\"输出序列化异常:{serializeEx.Message}\",\"traceId\":\"{traceId}\"}}";
                }
            }

            return outputJson;
        }

        private string FormatResponse(int code, string msg, string traceId)
        {
            try
            {
                OutputParam outputParam = new OutputParam()
                {
                    code = code,
                    msg = msg,
                    traceId = traceId
                };
                return Common.JsonHelper.Serializer(outputParam);
            }
            catch (Exception ex)
            {
                // FormatResponse序列化异常处理
                S_Base.sBase.Log.Error($"GoodsInfoSync FormatResponse序列化异常_异常[{ex.Message}]_traceId[{traceId}]");
                return $"{{\"code\":1,\"msg\":\"FormatResponse序列化异常:{ex.Message}\",\"traceId\":\"{traceId}\"}}";
            }
        }

        /// <summary>
        /// 处理单个物料信息
        /// </summary>
        /// <param name="inputParam">物料信息参数</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>错误消息，成功时返回空字符串</returns>
        private string ProcessSingleGoods(InputParam inputParam, string traceId)
        {
            try
            {
                // 必填字段验证
                if (string.IsNullOrEmpty(inputParam?.goodsCode))
                {
                    return "物料编码[goodsCode]不能为空";
                }

                if (string.IsNullOrEmpty(inputParam.name))
                {
                    return "物料名称[name]不能为空";
                }

                // 验证物资状态 (应为 0 或 1)
                if (inputParam.goodsStatus != 0 && inputParam.goodsStatus != 1)
                {
                    return $"物资状态[goodsStatus]值{inputParam.goodsStatus}有误，应为0或1";
                }

                // 检查物资是否已存在
                Model.GOODS_MAIN goodsMain = null;
                try
                {
                    goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(inputParam.goodsCode);
                }
                catch (Exception dbEx)
                {
                    S_Base.sBase.Log.Error($"查询物资信息异常_物资编码[{inputParam.goodsCode}]_异常[{dbEx.Message}]_traceId[{traceId}]");
                    return $"查询物资信息异常: {dbEx.Message}";
                }

                if (goodsMain == null)
                {
                    // 创建新物资
                    try
                    {
                        goodsMain = new Model.GOODS_MAIN()
                        {
                            GOODS_CLASS_ID = 1,
                            GOODS_CODE = inputParam.goodsCode,
                            GOODS_NAME = inputParam.name,
                            GOODS_FLAG = inputParam.goodsStatus.ToString(),
                            GOODS_UNITS = inputParam.unitName ?? string.Empty, //单位名称
                            GOODS_CONST_PROPERTY1 = inputParam.goodsVersion ?? string.Empty, // 规格型号
                            GOODS_CONST_PROPERTY2 = inputParam.brandVOs?.FirstOrDefault()?.brandName ?? string.Empty,//品牌
                        };

                        S_Base.sBase.pGOODS_MAIN.Add(goodsMain);
                        S_Base.sBase.Log.Info($"物资信息创建成功_物资编码[{inputParam.goodsCode}]_traceId[{traceId}]");
                    }
                    catch (Exception addEx)
                    {
                        S_Base.sBase.Log.Error($"创建物资信息异常_物资编码[{inputParam.goodsCode}]_异常[{addEx.Message}]_traceId[{traceId}]");
                        return $"创建物资信息异常: {addEx.Message}";
                    }
                }
                else
                {
                    // 更新现有物资
                    try
                    {
                        goodsMain.GOODS_CLASS_ID = 1;
                        goodsMain.GOODS_NAME = inputParam.name;
                        goodsMain.GOODS_FLAG = inputParam.goodsStatus.ToString();
                        goodsMain.GOODS_UNITS = inputParam.unitName ?? string.Empty;
                        goodsMain.GOODS_CONST_PROPERTY1 = inputParam.goodsVersion ?? string.Empty; // 规格型号
                        goodsMain.GOODS_CONST_PROPERTY2 = inputParam.brandVOs?.FirstOrDefault()?.brandName ?? string.Empty;//品牌
                        S_Base.sBase.pGOODS_MAIN.Update(goodsMain);
                        S_Base.sBase.Log.Info($"物资信息更新成功_物资编码[{inputParam.goodsCode}]_traceId[{traceId}]");
                    }
                    catch (Exception updateEx)
                    {
                        S_Base.sBase.Log.Error($"更新物资信息异常_物资编码[{inputParam.goodsCode}]_异常[{updateEx.Message}]_traceId[{traceId}]");
                        return $"更新物资信息异常: {updateEx.Message}";
                    }
                }

                // 处理嵌套的单位信息
                if (inputParam.unitInformationEntityDTO != null)
                {
                    try
                    {
                        ProcessUnitInformation(inputParam.unitInformationEntityDTO, inputParam.goodsCode);
                    }
                    catch (Exception unitEx)
                    {
                        S_Base.sBase.Log.Error($"处理单位信息异常_物资编码[{inputParam.goodsCode}]_异常[{unitEx.Message}]_traceId[{traceId}]");
                        // 单位信息处理失败不影响主流程，只记录日志
                    }
                }

                // 处理嵌套的品牌信息
                if (inputParam.brandVOs != null && inputParam.brandVOs.Count > 0)
                {
                    try
                    {
                        ProcessBrandInformation(inputParam.brandVOs, inputParam.goodsCode);
                    }
                    catch (Exception brandEx)
                    {
                        S_Base.sBase.Log.Error($"处理品牌信息异常_物资编码[{inputParam.goodsCode}]_异常[{brandEx.Message}]_traceId[{traceId}]");
                        // 品牌信息处理失败不影响主流程，只记录日志
                    }
                }

                S_Base.sBase.Log.Info($"单个物资处理成功_物资编码[{inputParam.goodsCode}]_traceId[{traceId}]");
                return string.Empty; // 成功时返回空字符串
            }
            catch (Exception ex)
            {
                string errorMsg = $"处理异常: {ex.Message}";
                S_Base.sBase.Log.Error($"单个物资处理异常_物资编码[{inputParam?.goodsCode ?? "未知"}]_异常[{ex.Message}]_堆栈[{ex.StackTrace}]_traceId[{traceId}]");
                return errorMsg;
            }
        }

        private string BuildGoodsRemark(InputParam inputParam)
        {
            try
            {
                if (inputParam == null)
                {
                    S_Base.sBase.Log.Warning("BuildGoodsRemark输入参数为空");
                    return string.Empty;
                }

                var remarkParts = new List<string>();

                try
                {
                    if (!string.IsNullOrEmpty(inputParam.daLeiName))
                        remarkParts.Add($"大类:{inputParam.daLeiName}");
                    if (!string.IsNullOrEmpty(inputParam.xiaoLeiName))
                        remarkParts.Add($"小类:{inputParam.xiaoLeiName}");
                    if (!string.IsNullOrEmpty(inputParam.daZuName))
                        remarkParts.Add($"大组:{inputParam.daZuName}");
                    if (!string.IsNullOrEmpty(inputParam.xiaoZuName))
                        remarkParts.Add($"小组:{inputParam.xiaoZuName}");
                    if (!string.IsNullOrEmpty(inputParam.goodsClass))
                        remarkParts.Add($"物资分类:{inputParam.goodsClass}");
                    if (!string.IsNullOrEmpty(inputParam.parentName))
                        remarkParts.Add($"父级:{inputParam.parentName}");
                }
                catch (Exception buildEx)
                {
                    S_Base.sBase.Log.Error($"BuildGoodsRemark构建备注部分异常_异常[{buildEx.Message}]");
                    return string.Empty;
                }

                return string.Join("|", remarkParts);
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"BuildGoodsRemark异常_异常[{ex.Message}]_堆栈[{ex.StackTrace}]");
                return string.Empty;
            }
        }

        private void ProcessUnitInformation(UnitInformationEntityDTO unitInfo, string goodsCode)
        {
            try
            {
                // 参数验证
                if (unitInfo == null)
                {
                    S_Base.sBase.Log.Warning($"单位信息为空_物资编码[{goodsCode}]");
                    return;
                }

                if (string.IsNullOrEmpty(goodsCode))
                {
                    S_Base.sBase.Log.Warning($"物资编码为空_无法处理单位信息");
                    return;
                }

                // Store unit information in GOODS_MAIN extended properties or create separate unit records
                // This is a placeholder for unit information processing
                // In a real implementation, you might store this in a separate GOODS_UNIT table

                if (unitInfo.conversionDetailVOs != null && unitInfo.conversionDetailVOs.Count > 0)
                {
                    // Process conversion details
                    foreach (var conversion in unitInfo.conversionDetailVOs)
                    {
                        try
                        {
                            if (conversion != null)
                            {
                                // Store conversion ratios - this would typically go to a separate table
                                S_Base.sBase.Log.Info($"处理单位换算信息_物资编码[{goodsCode}]_换算比率[{conversion.conversionRatio ?? "空"}]_换算单位[{conversion.conversionUnit ?? "空"}]");
                            }
                        }
                        catch (Exception conversionEx)
                        {
                            S_Base.sBase.Log.Error($"处理单位换算信息异常_物资编码[{goodsCode}]_异常[{conversionEx.Message}]");
                        }
                    }
                }

                S_Base.sBase.Log.Info($"处理单位信息成功_物资编码[{goodsCode}]_毛重[{unitInfo.grossWeight}]_净重[{unitInfo.netWeight}]");
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"处理单位信息异常_物资编码[{goodsCode ?? "未知"}]_异常[{ex.Message}]_堆栈[{ex.StackTrace}]");
                throw; // 重新抛出异常，让调用方处理
            }
        }

        private void ProcessBrandInformation(List<BrandVO> brands, string goodsCode)
        {
            try
            {
                // 参数验证
                if (brands == null || brands.Count == 0)
                {
                    S_Base.sBase.Log.Warning($"品牌信息为空_物资编码[{goodsCode}]");
                    return;
                }

                if (string.IsNullOrEmpty(goodsCode))
                {
                    S_Base.sBase.Log.Warning($"物资编码为空_无法处理品牌信息");
                    return;
                }

                // Process brand information
                // This would typically be stored in a separate GOODS_BRAND table
                foreach (var brand in brands)
                {
                    try
                    {
                        if (brand != null)
                        {
                            S_Base.sBase.Log.Info($"处理品牌信息_物资编码[{goodsCode}]_品牌[{brand.brandName ?? "空"}]_采购次数[{brand.purchasesNum ?? "空"}]_入库数量[{brand.inboundQuantity}]");
                        }
                        else
                        {
                            S_Base.sBase.Log.Warning($"品牌信息对象为空_物资编码[{goodsCode}]");
                        }
                    }
                    catch (Exception brandEx)
                    {
                        S_Base.sBase.Log.Error($"处理单个品牌信息异常_物资编码[{goodsCode}]_异常[{brandEx.Message}]");
                    }
                }

                S_Base.sBase.Log.Info($"处理品牌信息成功_物资编码[{goodsCode}]_品牌数量[{brands.Count}]");
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"处理品牌信息异常_物资编码[{goodsCode ?? "未知"}]_异常[{ex.Message}]_堆栈[{ex.StackTrace}]");
                throw; // 重新抛出异常，让调用方处理
            }
        }
    }
}
