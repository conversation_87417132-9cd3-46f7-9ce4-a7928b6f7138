using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 4.16 同步物料信息接口 【SSWMS提供，iWMS调用】
    /// 支持批量处理物料信息数组
    /// </summary>
    public class GoodsInfoSync : InterfaceBase
    {
        /// <summary>
        /// 批量输入参数包装类
        /// </summary>
        class BatchInputParam
        {
            /// <summary>
            /// 物料信息数组
            /// </summary>
            public List<InputParam> goodsList { get; set; }
        }

        /// <summary>
        /// 单个物料信息参数类
        /// </summary>
        class InputParam
        {
            public string daLeiId { get; set; }
            public string daLeiName { get; set; }
            public string xiaoLeiId { get; set; }
            public string xiaoLeiName { get; set; }
            public string daZuId { get; set; }
            public string daZuName { get; set; }
            public string xiaoZuId { get; set; }
            public string xiaoZuName { get; set; }
            public int goodsStatus { get; set; }
            public string code { get; set; }
            public string parentId { get; set; }
            public string parentName { get; set; }
            /// <summary>
            /// 物资名称（GOODS_NAME）
            /// </summary>
            public string name { get; set; }
            public string goodsType { get; set; }
            public string goodsAttribute { get; set; }
            /// <summary>
            /// 计量单位ID（GOODS_CONST_PROPERTY3）
            /// </summary>
            public string unitId { get; set; }
            /// <summary>
            /// 计量单位名称（GOODS_UNIT）
            /// </summary>
            public string unitName { get; set; }
            /// <summary>
            /// 规格型号（GOODS_CONST_PROPERTY2）
            /// </summary>
            public string goodsVersion { get; set; }
            public string goodsClass { get; set; }
            /// <summary>
            /// 物资编码（GOODS_CODE）
            /// </summary>
            public string goodsCode { get; set; }
            public decimal suggestedAmount { get; set; }
            public int isLaborInsuranceMaterials { get; set; }
            public bool ignoreParent { get; set; }
            public string fixedAssetFlag { get; set; }
            public string professionalAssetFlag { get; set; }
            public UnitInformationEntityDTO unitInformationEntityDTO { get; set; }
            public List<BrandVO> brandVOs { get; set; }
            public string isControlledByProductDate { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class UnitInformationEntityDTO
        {
            /// <summary>
            /// 物资ID（GOODS_CONST_PROPERTY1)
            /// </summary>
            public string goodsId { get; set; }
            public List<ConversionDetailVO> conversionDetailVOs { get; set; }
            public int grossWeight { get; set; }
            public int netWeight { get; set; }
            public int unitHeight { get; set; }
            public int unitWide { get; set; }
            public int unitLength { get; set; }
            public string unit { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class ConversionDetailVO
        {
            public string conversionRatio { get; set; }
            public string conversionUnit { get; set; }
        }

        class BrandVO
        {
            /// <summary>
            /// 品牌（GOODS_CONST_PROPERTY4）
            /// </summary>
            public string brandName { get; set; }
            public string purchasesNum { get; set; }
            public string initialPutawayTime { get; set; }
            public int inboundQuantity { get; set; }
            public string remark { get; set; }
        }

        /// <summary>
        /// 批量处理输出参数类
        /// </summary>
        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
            /// <summary>
            /// 批量处理结果详情
            /// </summary>
            public List<GoodsProcessResult> results { get; set; }
        }

        /// <summary>
        /// 单个物料处理结果类
        /// </summary>
        class GoodsProcessResult
        {
            /// <summary>
            /// 物料编码
            /// </summary>
            public string goodsCode { get; set; }
            /// <summary>
            /// 处理状态：0-成功，1-失败
            /// </summary>
            public int status { get; set; }
            /// <summary>
            /// 处理消息
            /// </summary>
            public string message { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            int resultCode = 0;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();
            List<GoodsProcessResult> processResults = new List<GoodsProcessResult>();

            try
            {
                // 尝试解析为批量输入参数
                BatchInputParam batchInputParam = null;
                List<InputParam> inputParams = new List<InputParam>();

                // 首先尝试解析为批量格式
                try
                {
                    batchInputParam = Common.JsonHelper.Deserialize<BatchInputParam>(inputJson);
                    if (batchInputParam != null && batchInputParam.goodsList != null && batchInputParam.goodsList.Count > 0)
                    {
                        inputParams = batchInputParam.goodsList;
                        S_Base.sBase.Log.Info($"GoodsInfoSync接收到批量数据_数量[{inputParams.Count}]_traceId[{traceId}]");
                    }
                }
                catch
                {
                    // 批量解析失败，尝试解析为单个对象
                }

                // 如果批量解析失败，尝试解析为单个对象（向后兼容）
                if (inputParams.Count == 0)
                {
                    try
                    {
                        InputParam singleInputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                        if (singleInputParam != null)
                        {
                            inputParams.Add(singleInputParam);
                            S_Base.sBase.Log.Info($"GoodsInfoSync接收到单个数据_traceId[{traceId}]");
                        }
                    }
                    catch
                    {
                        // 单个对象解析也失败
                    }
                }

                // 如果两种解析都失败
                if (inputParams.Count == 0)
                {
                    resultCode = 2;
                    message = $"调用入参[{inputJson}]解析错误，无法解析为有效的物料信息格式";
                    return FormatResponse(resultCode, message, traceId, processResults);
                }

                // 批量处理每个物料信息
                int successCount = 0;
                int failCount = 0;

                foreach (var inputParam in inputParams)
                {
                    var result = ProcessSingleGoods(inputParam, traceId);
                    processResults.Add(result);

                    if (result.status == 0)
                        successCount++;
                    else
                        failCount++;
                }

                // 设置处理结果汇总信息
                if (failCount == 0)
                {
                    resultCode = 0;
                    message = $"批量处理完成_总数[{inputParams.Count}]_成功[{successCount}]_失败[{failCount}]";
                }
                else if (successCount > 0)
                {
                    resultCode = 1; // 部分成功
                    message = $"批量处理部分成功_总数[{inputParams.Count}]_成功[{successCount}]_失败[{failCount}]";
                }
                else
                {
                    resultCode = 2; // 全部失败
                    message = $"批量处理失败_总数[{inputParams.Count}]_成功[{successCount}]_失败[{failCount}]";
                }

                // Log the operation
                S_Base.sBase.Log.Info($"GoodsInfoSync批量处理完成_入参长度[{inputJson.Length}]_结果[{message}]_traceId[{traceId}]");
            }
            catch (Exception ex)
            {
                resultCode = 1;
                message = string.Format("批量处理异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"GoodsInfoSync批量处理异常_入参长度[{inputJson?.Length ?? 0}]_异常[{ex.Message}]_traceId[{traceId}]");
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = resultCode,
                    msg = message,
                    traceId = traceId,
                    results = processResults
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        private string FormatResponse(int code, string msg, string traceId, List<GoodsProcessResult> results = null)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = code,
                msg = msg,
                traceId = traceId,
                results = results ?? new List<GoodsProcessResult>()
            };
            return Common.JsonHelper.Serializer(outputParam);
        }

        /// <summary>
        /// 处理单个物料信息
        /// </summary>
        /// <param name="inputParam">物料信息参数</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>处理结果</returns>
        private GoodsProcessResult ProcessSingleGoods(InputParam inputParam, string traceId)
        {
            var result = new GoodsProcessResult()
            {
                goodsCode = inputParam?.goodsCode ?? "未知",
                status = 0,
                message = string.Empty
            };

            try
            {
                // 必填字段验证
                if (string.IsNullOrEmpty(inputParam.goodsCode))
                {
                    result.status = 1;
                    result.message = "物料编码[goodsCode]不能为空";
                    return result;
                }

                if (string.IsNullOrEmpty(inputParam.name))
                {
                    result.status = 1;
                    result.message = "物料名称[name]不能为空";
                    return result;
                }

                // 验证物资状态 (应为 0 或 1)
                if (inputParam.goodsStatus != 0 && inputParam.goodsStatus != 1)
                {
                    result.status = 1;
                    result.message = $"物资状态[goodsStatus]值{inputParam.goodsStatus}有误，应为0或1";
                    return result;
                }

                // 检查物资是否已存在
                Model.GOODS_MAIN goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(inputParam.goodsCode);
                if (goodsMain == null)
                {
                    // 创建新物资
                    goodsMain = new Model.GOODS_MAIN()
                    {
                        GOODS_CLASS_ID = 1,
                        GOODS_CODE = inputParam.goodsCode,
                        GOODS_NAME = inputParam.name,
                        GOODS_FLAG = inputParam.goodsStatus.ToString(),
                        GOODS_UNITS = inputParam.unitName ?? string.Empty, //单位名称
                        GOODS_CONST_PROPERTY1 = inputParam.goodsVersion ?? string.Empty, // 规格型号
                        GOODS_CONST_PROPERTY2 = inputParam.brandVOs?.FirstOrDefault()?.brandName ?? string.Empty,//品牌
                    };

                    S_Base.sBase.pGOODS_MAIN.Add(goodsMain);
                    result.message = "物资信息创建成功";
                }
                else
                {
                    // 更新现有物资
                    goodsMain.GOODS_CLASS_ID = 1;
                    goodsMain.GOODS_NAME = inputParam.name;
                    goodsMain.GOODS_FLAG = inputParam.goodsStatus.ToString();
                    goodsMain.GOODS_UNITS = inputParam.unitName ?? string.Empty;
                    goodsMain.GOODS_CONST_PROPERTY1 = inputParam.goodsVersion ?? string.Empty; // 规格型号
                    goodsMain.GOODS_CONST_PROPERTY2 = inputParam.brandVOs?.FirstOrDefault()?.brandName ?? string.Empty;//品牌
                    S_Base.sBase.pGOODS_MAIN.Update(goodsMain);
                    result.message = "物资信息更新成功";
                }

                // 处理嵌套的单位信息
                if (inputParam.unitInformationEntityDTO != null)
                {
                    ProcessUnitInformation(inputParam.unitInformationEntityDTO, inputParam.goodsCode);
                }

                // 处理嵌套的品牌信息
                if (inputParam.brandVOs != null && inputParam.brandVOs.Count > 0)
                {
                    ProcessBrandInformation(inputParam.brandVOs, inputParam.goodsCode);
                }

                S_Base.sBase.Log.Info($"单个物资处理成功_物资编码[{inputParam.goodsCode}]_traceId[{traceId}]");
            }
            catch (Exception ex)
            {
                result.status = 1;
                result.message = $"处理异常: {ex.Message}";
                S_Base.sBase.Log.Error($"单个物资处理异常_物资编码[{inputParam?.goodsCode ?? "未知"}]_异常[{ex.Message}]_traceId[{traceId}]");
            }

            return result;
        }

        private string BuildGoodsRemark(InputParam inputParam)
        {
            var remarkParts = new List<string>();
            
            if (!string.IsNullOrEmpty(inputParam.daLeiName))
                remarkParts.Add($"大类:{inputParam.daLeiName}");
            if (!string.IsNullOrEmpty(inputParam.xiaoLeiName))
                remarkParts.Add($"小类:{inputParam.xiaoLeiName}");
            if (!string.IsNullOrEmpty(inputParam.daZuName))
                remarkParts.Add($"大组:{inputParam.daZuName}");
            if (!string.IsNullOrEmpty(inputParam.xiaoZuName))
                remarkParts.Add($"小组:{inputParam.xiaoZuName}");
            if (!string.IsNullOrEmpty(inputParam.goodsClass))
                remarkParts.Add($"物资分类:{inputParam.goodsClass}");
            if (!string.IsNullOrEmpty(inputParam.parentName))
                remarkParts.Add($"父级:{inputParam.parentName}");

            return string.Join("|", remarkParts);
        }

        private void ProcessUnitInformation(UnitInformationEntityDTO unitInfo, string goodsCode)
        {
            try
            {
                // Store unit information in GOODS_MAIN extended properties or create separate unit records
                // This is a placeholder for unit information processing
                // In a real implementation, you might store this in a separate GOODS_UNIT table
                
                if (unitInfo.conversionDetailVOs != null && unitInfo.conversionDetailVOs.Count > 0)
                {
                    // Process conversion details
                    foreach (var conversion in unitInfo.conversionDetailVOs)
                    {
                        // Store conversion ratios - this would typically go to a separate table
                        S_Base.sBase.Log.Info($"处理单位换算信息_物资编码[{goodsCode}]_换算比率[{conversion.conversionRatio}]_换算单位[{conversion.conversionUnit}]");
                    }
                }

                S_Base.sBase.Log.Info($"处理单位信息成功_物资编码[{goodsCode}]_毛重[{unitInfo.grossWeight}]_净重[{unitInfo.netWeight}]");
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"处理单位信息异常_物资编码[{goodsCode}]_异常[{ex.Message}]");
            }
        }

        private void ProcessBrandInformation(List<BrandVO> brands, string goodsCode)
        {
            try
            {
                // Process brand information
                // This would typically be stored in a separate GOODS_BRAND table
                foreach (var brand in brands)
                {
                    S_Base.sBase.Log.Info($"处理品牌信息_物资编码[{goodsCode}]_品牌[{brand.brandName}]_采购次数[{brand.purchasesNum}]_入库数量[{brand.inboundQuantity}]");
                }

                S_Base.sBase.Log.Info($"处理品牌信息成功_物资编码[{goodsCode}]_品牌数量[{brands.Count}]");
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"处理品牌信息异常_物资编码[{goodsCode}]_异常[{ex.Message}]");
            }
        }
    }
}
