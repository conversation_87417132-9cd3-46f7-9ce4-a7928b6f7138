using SiaSun.LMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 盘点计划表接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class InventoryPlanSync : InterfaceBase
    {
        class InputParam
        {
            /// <summary>
            /// 盘点计划名称（BACKUP_FIELD2）
            /// </summary>
            public string stockTakeName { get; set; }
            /// <summary>
            /// 盘点计划单编号（PLAN_CODE）
            /// </summary>
            public string stockTakeCode { get; set; }
            public string description { get; set; }
            public string stockTakeType { get; set; }
            public string stockTakeWay { get; set; }
            public string stockTakeStartDate { get; set; }
            public string stockTakeEndDate { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public string operatorId { get; set; }
            public string operatorName { get; set; }
            public string stockTakeManageId { get; set; }
            public string stockTakeManageName { get; set; }
            public List<StockTakeWarehouseItem> stockTakeWarehouseList { get; set; }
            public List<StockTakeGoodsItem> stockTakeGoodsList { get; set; }
            public string stockTakeMangeDeptId { get; set; }
            public string stockTakeMangeDeptName { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class StockTakeWarehouseItem
        {
            /// <summary>
            /// 盘点计划id（BACKUP_FIELD1）
            /// </summary>
            public string stockTakeId { get; set; }
            public string warehouseId { get; set; }
            public string warehouseCode { get; set; }
            public string warehouseName { get; set; }
            public string highGoods { get; set; }
            public string stockTakeRatio { get; set; }
            public string shelfId { get; set; }
            public string shelfName { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class StockTakeGoodsItem
        {
            /// <summary>
            /// 库存 ID（BACKUP_FIELD3）
            /// </summary>
            public string inventoryId { get; set; }
            public string stockTakeId { get; set; }
            public string goodsId { get; set; }
            public string goodsCode { get; set; }
            public string goodsName { get; set; }
            public string goodsVersion { get; set; }
            public int storageNum { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public string brand { get; set; }
            public string warehouseId { get; set; }
            public string warehouseName { get; set; }
            public string warehouseCode { get; set; }
            public string shelfId { get; set; }
            public string shelfName { get; set; }
            public string shelfCode { get; set; }
            /// <summary>
            /// 盘点人ID（BACKUP_FIELD4）
            /// </summary>
            public string stockTakeUserId { get; set; }
            /// <summary>
            /// 盘点人姓名（PLAN_TO_USER）
            /// </summary>
            public string stockTakeUserName { get; set; }
            /// <summary>
            /// 盘点开始日期(PLAN_BEGIN_TIME)
            /// </summary>
            public string stockTakePlanStartDate { get; set; }
            /// <summary>
            /// 盘点结束日期(PLAN_END_TIME)
            /// </summary>
            public string stockTakePlanEndDate { get; set; }
            /// <summary>
            /// 创建时间(PLAN_CREATE_TIME)
            /// </summary>
            public string createDate { get; set; }
            /// <summary>
            /// 创建人(BACKUP_FIELD5)
            /// </summary>
            public string createUser { get; set; }
            /// <summary>
            /// 创建人姓名（PLAN_FROM_USER）
            /// </summary>
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }

                // 验证必填字段
                if (string.IsNullOrEmpty(inputParam.stockTakeName) || string.IsNullOrEmpty(inputParam.stockTakeCode) ||
                    string.IsNullOrEmpty(inputParam.stockTakeType) || string.IsNullOrEmpty(inputParam.stockTakeStartDate) ||
                    string.IsNullOrEmpty(inputParam.stockTakeEndDate))
                {
                    result = false;
                    message = "接口入参必填项存在空值：stockTakeName, stockTakeCode, stockTakeType, stockTakeStartDate, stockTakeEndDate";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }

                // 验证日期格式
                DateTime startDate, endDate;
                if (!DateTime.TryParse(inputParam.stockTakeStartDate, out startDate))
                {
                    result = false;
                    message = $"盘点开始日期格式错误：{inputParam.stockTakeStartDate}";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }

                if (!DateTime.TryParse(inputParam.stockTakeEndDate, out endDate))
                {
                    result = false;
                    message = $"盘点结束日期格式错误：{inputParam.stockTakeEndDate}";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }

                // 验证日期逻辑
                if (startDate > endDate)
                {
                    result = false;
                    message = "盘点开始日期不能晚于结束日期";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }

                // 验证盘点仓库列表
                if (inputParam.stockTakeWarehouseList == null || inputParam.stockTakeWarehouseList.Count == 0)
                {
                    result = false;
                    message = "盘点仓库列表不能为空";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }

                // 验证盘点仓库项必填字段
                foreach (var warehouseItem in inputParam.stockTakeWarehouseList)
                {
                    if (string.IsNullOrEmpty(warehouseItem.warehouseId) || string.IsNullOrEmpty(warehouseItem.warehouseCode) ||
                        string.IsNullOrEmpty(warehouseItem.warehouseName))
                    {
                        result = false;
                        message = "盘点仓库项必填字段存在空值：warehouseId, warehouseCode, warehouseName";
                        return FormatInventoryPlanErrorMessage(message, traceId);
                    }
                }

                // 验证盘点物资列表
                if (inputParam.stockTakeGoodsList == null || inputParam.stockTakeGoodsList.Count == 0)
                {
                    result = false;
                    message = "盘点物资列表不能为空";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }

                // 验证盘点物资项必填字段
                foreach (var goodsItem in inputParam.stockTakeGoodsList)
                {
                    if (string.IsNullOrEmpty(goodsItem.goodsCode))
                    {
                        result = false;
                        message = "盘点物资项必填字段存在空值：goodsCode";
                        return FormatInventoryPlanErrorMessage(message, traceId);
                    }

                    // 验证盘点物资的日期格式
                    if (!string.IsNullOrEmpty(goodsItem.stockTakePlanStartDate))
                    {
                        DateTime goodsStartDate;
                        if (!DateTime.TryParse(goodsItem.stockTakePlanStartDate, out goodsStartDate))
                        {
                            result = false;
                            message = $"物资盘点开始日期格式错误：{goodsItem.stockTakePlanStartDate}";
                            return FormatInventoryPlanErrorMessage(message, traceId);
                        }
                    }

                    if (!string.IsNullOrEmpty(goodsItem.stockTakePlanEndDate))
                    {
                        DateTime goodsEndDate;
                        if (!DateTime.TryParse(goodsItem.stockTakePlanEndDate, out goodsEndDate))
                        {
                            result = false;
                            message = $"物资盘点结束日期格式错误：{goodsItem.stockTakePlanEndDate}";
                            return FormatInventoryPlanErrorMessage(message, traceId);
                        }
                    }
                }

                // 检查盘点计划单是否已存在
                var existingReceipt = S_Base.sBase.pPLAN_MAIN.GetModelPlanCode(inputParam.stockTakeCode);

                if (existingReceipt != null)
                {
                    result = false;
                    message = $"已存在盘点计划单-{inputParam.stockTakeCode}";
                    return FormatInventoryPlanErrorMessage(message, traceId);
                }
                else
                {
                    // 处理盘点计划数据
                    // 这里应该包含：
                    // 1. 创建或更新盘点计划主表记录
                    // 2. 处理盘点仓库数据
                    // 3. 处理盘点物资数据
                    // 4. 记录操作日志
                    PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN()
                    {
                        PLAN_CODE = inputParam.stockTakeCode,
                        PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanCommonDown.ToString(),
                        PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString(),
                        PLAN_CREATER = inputParam.createUser,
                        PLAN_FLAG = "1",
                        PLAN_TO_USER = inputParam.stockTakeGoodsList[0].stockTakeUserName,
                        //PLAN_BEGIN_TIME = inputParam.stockTakeGoodsList[0].stockTakePlanStartDate,
                        //PLAN_END_TIME = inputParam.stockTakeGoodsList[0].stockTakePlanEndDate,
                        PLAN_CREATE_TIME = inputParam.stockTakeGoodsList[0].createDate,
                    };

                    IList<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();
                    foreach (StockTakeGoodsItem item in inputParam.stockTakeGoodsList)
                    {
                        PLAN_LIST mPLAN_LIST = new PLAN_LIST()
                        {
                            PLAN_LIST_QUANTITY = item.storageNum,
                            GOODS_ID = S_Base.sBase.pGOODS_MAIN.GetModel(item.goodsCode).GOODS_ID,

                        };
                        lsPLAN_LIST.Add(mPLAN_LIST);
                    }

                    result = S_Base.sBase.sPlan.PlanCreate(mPLAN_MAIN, lsPLAN_LIST, true, out int planID, out message);

                }

                // 模拟数据处理成功
                S_Base.sBase.Log.Info($"盘点计划同步成功 - 计划编码: {inputParam.stockTakeCode}, 计划名称: {inputParam.stockTakeName}, " +
                    $"盘点类型: {inputParam.stockTakeType}, 仓库数量: {inputParam.stockTakeWarehouseList.Count}, " +
                    $"物资数量: {inputParam.stockTakeGoodsList.Count}");

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"盘点计划同步异常: {ex.Message}", ex);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = result ? 0 : 1,
                    msg = result ? "成功" : message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        /// <summary>
        /// 格式化盘点计划错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>格式化的错误响应</returns>
        private string FormatInventoryPlanErrorMessage(string message, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = 2, // 入参错误
                msg = message,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}